<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

# Complete AI Coding Assistant Instructions for React Native S3 Storage App

Here are detailed, step-by-step instructions you can copy and paste into any AI coding assistant (like <PERSON>tGP<PERSON>, Claude, <PERSON>ursor AI, etc.) to build your React Native S3 storage app. Each section is designed to be fed to the AI tool sequentially.

## Phase 1: Project Setup Instructions

Copy this to your AI coding assistant:

```
I want to create a React Native app that works as a Google Drive-like interface for AWS S3 storage. Here are the exact requirements:

PROJECT REQUIREMENTS:
- React Native with TypeScript
- Users input their AWS S3 credentials (Access Key, Secret Key, Region, Bucket Name)
- Store credentials securely on device using react-native-keychain
- No backend required - direct S3 communication
- Google Drive-like UI with file/folder management

FEATURES NEEDED:
- Secure credential storage and setup screen
- File upload/download with progress bars
- Create/rename/delete folders
- Move/copy files
- Search and filter files
- Share files via presigned URLs
- Grid/list view toggle
- File thumbnails for images

TECHNOLOGY STACK:
- React Native with TypeScript
- @aws-sdk/client-s3 for S3 operations
- react-native-keychain for secure storage
- @react-navigation/native for navigation
- react-native-document-picker for file selection
- react-native-elements for UI components
- react-native-vector-icons for icons
- react-native-progress for progress bars

Please create the initial React Native TypeScript project structure with all necessary dependencies and basic configuration files. Include package.json, metro.config.js, and platform-specific setup requirements.
```


## Phase 2: Core Architecture Setup

After Phase 1 is complete, use this:

```
Now create the core architecture for the S3 storage app:

DIRECTORY STRUCTURE:
src/
├── components/ (reusable UI components)
├── screens/ (main app screens)
├── services/ (S3, storage, file services)
├── utils/ (helper functions, constants)
└── types/ (TypeScript interfaces)

CORE INTERFACES NEEDED:
- AWSCredentials (accessKeyId, secretAccessKey, region, bucketName)
- S3Object (key, name, size, lastModified, isFolder, path)
- UploadProgress (loaded, total, percentage)

SERVICES TO CREATE:
1. StorageService: Secure credential storage using react-native-keychain
2. S3Service: All AWS S3 operations (list, upload, download, delete, copy)
3. FileService: Local file operations and utilities

Please create these TypeScript files with proper interfaces, error handling, and the StorageService class with methods to save/get/clear AWS credentials securely.
```


## Phase 3: S3 Service Implementation

Continue with:

```
Create a comprehensive S3Service class with the following methods:

REQUIRED S3 OPERATIONS:
- testConnection(): Validate AWS credentials
- listObjects(prefix): List files and folders at path
- uploadFile(file, key, progressCallback): Upload with progress tracking
- downloadFile(key): Generate signed download URL
- deleteObject(key): Delete file or folder
- copyObject(sourceKey, destKey): Copy/move files
- createFolder(path): Create new folder
- getShareableUrl(key, expiresIn): Generate presigned URLs

IMPLEMENTATION REQUIREMENTS:
- Use @aws-sdk/client-s3 and @aws-sdk/s3-request-presigner
- Proper error handling with user-friendly messages
- Support for folder operations (S3 prefixes)
- Progress tracking for uploads
- Signed URLs for downloads and sharing

Please implement the complete S3Service class with all these methods, including proper TypeScript types and error handling.
```


## Phase 4: UI Screens Development

Next phase:

```
Create the main UI screens for the app:

SCREENS NEEDED:

1. SetupScreen:
- Form for AWS credentials input
- Validation and connection testing
- Save credentials securely
- Clean, user-friendly design

2. HomeScreen:
- File/folder browser with breadcrumbs
- Grid/list view toggle
- Search bar with filtering
- Upload button (FAB style)
- Pull-to-refresh functionality

3. FileListScreen:
- Display files and folders
- File thumbnails for images
- Context menus for file actions
- Sorting options (name, date, size)
- Selection mode for bulk operations

4. SettingsScreen:
- View/edit AWS credentials
- Storage usage analytics
- App preferences
- Clear data option

DESIGN REQUIREMENTS:
- Material Design for Android, iOS native feel
- Consistent color scheme and typography
- Loading states and error handling
- Responsive design for different screen sizes

Please create these screen components with proper navigation setup using @react-navigation/native.
```


## Phase 5: File Operations UI

Continue with:

```
Create the file operation components and modals:

COMPONENTS NEEDED:

1. FileItem Component:
- Display file/folder with icon and metadata
- Thumbnail support for images
- Context menu for actions
- Selection checkbox for bulk operations

2. UploadModal:
- File selection interface
- Upload progress display
- Cancel/retry functionality
- Support for multiple files

3. ActionSheet:
- File actions (rename, move, copy, delete, share)
- Folder actions (rename, delete)
- Clean modal interface

4. ProgressModal:
- Download/upload progress
- Cancellation support
- Error handling display

5. SearchBar:
- Real-time search functionality
- Filter by file type
- Clear search option

FUNCTIONALITY REQUIREMENTS:
- File picker integration for uploads
- Progress tracking with visual feedback
- Error handling with retry options
- Offline state handling

Please implement these components with proper state management and user feedback.
```


## Phase 6: Navigation and State Management

Next:

```
Set up complete navigation and state management:

NAVIGATION STRUCTURE:
- Stack Navigator with proper screen transitions
- Tab Navigator for main sections (Files, Settings)
- Modal presentations for uploads and actions
- Back navigation with breadcrumbs

STATE MANAGEMENT:
- Context API or Redux for global state
- Manage current folder path
- File list caching
- Upload/download progress
- User credentials state

REQUIRED FEATURES:
- Deep linking to specific folders
- Navigation history
- Swipe gestures for mobile navigation
- Header customization per screen
- Loading states during navigation

Please create the complete navigation setup with proper TypeScript typing and state management solution.
```


## Phase 7: Security and Production Features

Final phase:

```
Implement security measures and production-ready features:

SECURITY REQUIREMENTS:
- Never log or expose AWS credentials
- Validate all user inputs
- Secure file type checking
- HTTPS enforcement
- Proper error messages without sensitive data

PRODUCTION FEATURES:
- Offline file caching
- Background upload/download
- Network error handling with retry
- File compression options
- Analytics and crash reporting setup

TESTING SETUP:
- Unit tests for services
- Integration tests for S3 operations
- UI component testing
- Mock AWS responses for testing

DEPLOYMENT PREPARATION:
- Build configurations for Android/iOS
- App signing setup
- Store listing requirements
- Privacy policy compliance

Please implement these security measures and production features, then provide build and deployment instructions for both Android and iOS platforms.
```