{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactFreeze", "e", "__esModule", "default", "DelayedFreeze", "freeze", "children", "freezeState", "setFreezeState", "React", "useState", "useEffect", "id", "setImmediate", "clearImmediate", "createElement", "Freeze", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/helpers/DelayedFreeze.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAAsC,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAOtC;AACA;AACA,SAASG,aAAaA,CAAC;EAAEC,MAAM;EAAEC;AAA6B,CAAC,EAAE;EAC/D;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGC,cAAK,CAACC,QAAQ,CAAC,KAAK,CAAC;EAE3DD,cAAK,CAACE,SAAS,CAAC,MAAM;IACpB,MAAMC,EAAE,GAAGC,YAAY,CAAC,MAAM;MAC5BL,cAAc,CAACH,MAAM,CAAC;IACxB,CAAC,CAAC;IACF,OAAO,MAAM;MACXS,cAAc,CAACF,EAAE,CAAC;IACpB,CAAC;EACH,CAAC,EAAE,CAACP,MAAM,CAAC,CAAC;EAEZ,oBAAOR,MAAA,CAAAM,OAAA,CAAAY,aAAA,CAACf,YAAA,CAAAgB,MAAM;IAACX,MAAM,EAAEA,MAAM,GAAGE,WAAW,GAAG;EAAM,GAAED,QAAiB,CAAC;AAC1E;AAAC,IAAAW,QAAA,GAAAC,OAAA,CAAAf,OAAA,GAEcC,aAAa", "ignoreList": []}