{"formatVersion": "1.1", "component": {"group": "host.exp.exponent", "module": "expo.modules.documentpicker", "version": "13.1.6", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.13"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.0.0"}}, {"group": "commons-io", "module": "commons-io", "version": {"requires": "2.6"}}], "files": [{"name": "expo.modules.documentpicker-13.1.6.aar", "url": "expo.modules.documentpicker-13.1.6.aar", "size": 21239, "sha512": "1309be3bf2ce6c80baa8f08926f512a48420c979e136dc714ce04389043e4bbc7b9d3095cc5b7d23646924dcd52cb60a55dac7d69fccea966c4b2d133e868b28", "sha256": "4598412ee49c5c5b6be39ae55fb6d92ab24be349a2b84a08435c18c198172846", "sha1": "0598572dd5682b650f9a9fc669828cf1ebeac053", "md5": "0d31bd0ba0f3d7ef36863c415de388b5"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7", "version": {"requires": "2.0.21"}}, {"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.0.0"}}, {"group": "commons-io", "module": "commons-io", "version": {"requires": "2.6"}}], "files": [{"name": "expo.modules.documentpicker-13.1.6.aar", "url": "expo.modules.documentpicker-13.1.6.aar", "size": 21239, "sha512": "1309be3bf2ce6c80baa8f08926f512a48420c979e136dc714ce04389043e4bbc7b9d3095cc5b7d23646924dcd52cb60a55dac7d69fccea966c4b2d133e868b28", "sha256": "4598412ee49c5c5b6be39ae55fb6d92ab24be349a2b84a08435c18c198172846", "sha1": "0598572dd5682b650f9a9fc669828cf1ebeac053", "md5": "0d31bd0ba0f3d7ef36863c415de388b5"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "expo.modules.documentpicker-13.1.6-sources.jar", "url": "expo.modules.documentpicker-13.1.6-sources.jar", "size": 4171, "sha512": "6cc2345cf99034cbe6881d8c963d165251c08b88d64137a9604c5fa6d971d83151ee6339faac371fc8f6e8f59aa623c976d5e2009c89474829bd3a43b92a6f55", "sha256": "defc2b49a0bff466871cdf77d13d9a9b7a5b4d44c386196398c0a2fea8900ffb", "sha1": "d766ab7ef42edf85be14c895ccf7985f14cdbb2d", "md5": "3aa8aff964adf24377ea92642dde30d3"}]}]}