
buildscript {
    ext.safeExtGet = {prop, fallback ->
        rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
    }
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath("com.android.tools.build:gradle:7.2.0")
    }
}

def getExtOrDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : project.properties['ReactNativePicker_' + name]
}

def getExtOrIntegerDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : (project.properties['ReactNativePicker_' + name]).toInteger()
}


def resolveReactNativeDirectory() {
    def reactNativeLocation = safeExtGet("REACT_NATIVE_NODE_MODULES_DIR", null)
    if (reactNativeLocation != null) {
        return file(reactNativeLocation)
    }

    // monorepo workaround
    // react-native can be hoisted or in project's own node_modules
    def reactNativeFromProjectNodeModules = file("${rootProject.projectDir}/../node_modules/react-native")
    if (reactNativeFromProjectNodeModules.exists()) {
        return reactNativeFromProjectNodeModules
    }

    def reactNativeFromNodeModulesWithPicker = file("${projectDir}/../../../react-native")
    if (reactNativeFromNodeModulesWithPicker.exists()) {
        return reactNativeFromNodeModulesWithPicker
    }

    throw new Exception(
            "[react-native-picker] Unable to resolve react-native location in " +
                    "node_modules. You should add project extension property (in app/build.gradle) " +
                    "`REACT_NATIVE_NODE_MODULES_DIR` with path to react-native."
    )
}

def getReactNativeMinorVersion() {
    def REACT_NATIVE_DIR = resolveReactNativeDirectory()

    def reactProperties = new Properties()
    file("$REACT_NATIVE_DIR/ReactAndroid/gradle.properties").withInputStream { reactProperties.load(it) }

    def REACT_NATIVE_VERSION = reactProperties.getProperty("VERSION_NAME")
    def REACT_NATIVE_MINOR_VERSION = REACT_NATIVE_VERSION.startsWith("0.0.0-") ? 1000 : REACT_NATIVE_VERSION.split("\\.")[1].toInteger()

    return REACT_NATIVE_MINOR_VERSION
}

def isNewArchitectureEnabled() {
    // To opt-in for the New Architecture, you can either:
    // - Set `newArchEnabled` to true inside the `gradle.properties` file
    // - Invoke gradle with `-newArchEnabled=true`
    // - Set an environment variable `ORG_GRADLE_PROJECT_newArchEnabled=true`
    return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
}

apply plugin: 'com.android.library'

if (isNewArchitectureEnabled()) {
    apply plugin: "com.facebook.react"
}

android {
    def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
  if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
    namespace "com.reactnativecommunity.picker"
  }

  compileSdkVersion safeExtGet('compileSdkVersion', 31)

    defaultConfig {
        minSdkVersion safeExtGet('minSdkVersion', 21)
        targetSdkVersion safeExtGet('targetSdkVersion', 31)
        vectorDrawables.useSupportLibrary=getExtOrDefault('vector_drawable_use_support_library')
        buildConfigField("boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString())
    }

    sourceSets.main {
        java {
            if (isNewArchitectureEnabled()) {
                srcDirs += [
                    "src/fabric/java",
                ]
            } else {
                srcDirs += [
                    "src/paper/java",
                ]
            }
        }
    }
}

repositories {
  maven {
      // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
      url "$projectDir/../node_modules/react-native/android"
  }
  google()
  mavenCentral()
}

dependencies {
    if (isNewArchitectureEnabled() && getReactNativeMinorVersion() < 71) {
        implementation project(":ReactAndroid")
    } else {
        implementation 'com.facebook.react:react-native:+'
    }
}
