import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Alert,
  FlatList,
  ScrollView,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import Icon from 'react-native-vector-icons/MaterialIcons';
import * as DocumentPicker from 'expo-document-picker';
import { FileService } from '../services';
import { COLORS, SPACING, FONT_SIZES } from '../utils/constants';

interface UploadModalProps {
  visible: boolean;
  currentPath: string;
  onClose: () => void;
  onFilesSelected: (files: DocumentPicker.DocumentPickerAsset[], path: string, options?: { storageClass?: string; serverSideEncryption?: string }) => void;
}

interface SelectedFile extends DocumentPicker.DocumentPickerAsset {
  id: string;
}

export const UploadModal: React.FC<UploadModalProps> = ({
  visible,
  currentPath,
  onClose,
  onFilesSelected,
}) => {
  const [selectedFiles, setSelectedFiles] = useState<SelectedFile[]>([]);
  const [isPickingFiles, setIsPickingFiles] = useState(false);
  const [storageClass, setStorageClass] = useState('GLACIER_IR'); // Default to Glacier Instant Retrieval
  const [serverSideEncryption, setServerSideEncryption] = useState('AES256'); // Default to SSE-S3

  const handlePickFiles = async () => {
    setIsPickingFiles(true);
    try {
      const result = await FileService.pickFiles(true);
      if (result.success && result.data && result.data.length > 0) {
        const filesWithIds = result.data.map((file, index) => ({
          ...file,
          id: `${Date.now()}_${index}`,
        }));
        setSelectedFiles(filesWithIds);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick files. Please try again.');
    } finally {
      setIsPickingFiles(false);
    }
  };

  const handlePickImages = async () => {
    setIsPickingFiles(true);
    try {
      const result = await FileService.pickImages(true);
      if (result.success && result.data && result.data.length > 0) {
        const filesWithIds = result.data.map((file, index) => ({
          ...file,
          id: `${Date.now()}_${index}`,
        }));
        setSelectedFiles(filesWithIds);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick images. Please try again.');
    } finally {
      setIsPickingFiles(false);
    }
  };

  const removeFile = (fileId: string) => {
    setSelectedFiles(files => files.filter(file => file.id !== fileId));
  };

  const handleUpload = () => {
    if (selectedFiles.length === 0) {
      Alert.alert('No Files', 'Please select files to upload.');
      return;
    }

    // Validate file names
    const invalidFiles = selectedFiles.filter(file => {
      const validation = FileService.validateFileName(file.name || '');
      return !validation.isValid;
    });

    if (invalidFiles.length > 0) {
      Alert.alert(
        'Invalid File Names',
        `Some files have invalid names: ${invalidFiles.map(f => f.name).join(', ')}`
      );
      return;
    }

    onFilesSelected(selectedFiles, currentPath, {
      storageClass,
      serverSideEncryption: serverSideEncryption === 'NONE' ? undefined : serverSideEncryption,
    });
    handleClose();
  };

  const handleClose = () => {
    setSelectedFiles([]);
    onClose();
  };

  const renderFileItem = ({ item }: { item: SelectedFile }) => {
    const fileType = FileService.getFileType(item.name || '');
    const iconName = getFileTypeIcon(fileType);
    const iconColor = getFileTypeColor(fileType);

    return (
      <View style={styles.fileItem}>
        <Icon name={iconName} size={32} color={iconColor} />
        <View style={styles.fileInfo}>
          <Text style={styles.fileName} numberOfLines={1}>
            {item.name}
          </Text>
          <Text style={styles.fileSize}>
            {item.size ? FileService.formatFileSize(item.size) : 'Unknown size'}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => removeFile(item.id)}
        >
          <Icon name="close" size={20} color={COLORS.ERROR} />
        </TouchableOpacity>
      </View>
    );
  };

  const getFileTypeIcon = (type: string) => {
    switch (type) {
      case 'image': return 'image';
      case 'video': return 'video-camera';
      case 'audio': return 'music';
      case 'document': return 'description';
      case 'archive': return 'archive';
      default: return 'insert-drive-file';
    }
  };

  const getFileTypeColor = (type: string) => {
    switch (type) {
      case 'image': return COLORS.SUCCESS;
      case 'video': return COLORS.ERROR;
      case 'audio': return COLORS.SECONDARY;
      case 'document': return COLORS.INFO;
      case 'archive': return COLORS.WARNING;
      default: return COLORS.GRAY;
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
            <Icon name="close" size={24} color={COLORS.TEXT_PRIMARY} />
          </TouchableOpacity>
          <Text style={styles.title}>Upload Files</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.pathContainer}>
          <Icon name="folder" size={20} color={COLORS.GRAY} />
          <Text style={styles.pathText}>
            Upload to: {currentPath || 'Root'}
          </Text>
        </View>

        <ScrollView style={styles.optionsContainer} showsVerticalScrollIndicator={false}>
          <View style={styles.optionSection}>
            <Text style={styles.optionLabel}>Storage Class</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={storageClass}
                onValueChange={(itemValue) => setStorageClass(itemValue)}
                style={styles.picker}
              >
                <Picker.Item label="Glacier Instant Retrieval (Recommended)" value="GLACIER_IR" />
                <Picker.Item label="Standard" value="STANDARD" />
                <Picker.Item label="Standard-IA" value="STANDARD_IA" />
                <Picker.Item label="One Zone-IA" value="ONEZONE_IA" />
                <Picker.Item label="Glacier Flexible Retrieval" value="GLACIER" />
                <Picker.Item label="Glacier Deep Archive" value="DEEP_ARCHIVE" />
              </Picker>
            </View>
          </View>

          <View style={styles.optionSection}>
            <Text style={styles.optionLabel}>Server-Side Encryption</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={serverSideEncryption}
                onValueChange={(itemValue) => setServerSideEncryption(itemValue)}
                style={styles.picker}
              >
                <Picker.Item label="SSE-S3 (Free)" value="AES256" />
                <Picker.Item label="No Encryption" value="NONE" />
              </Picker>
            </View>
          </View>
        </ScrollView>

        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.actionButton, styles.primaryButton]}
            onPress={handlePickFiles}
            disabled={isPickingFiles}
          >
            <Icon name="attach-file" size={24} color={COLORS.BACKGROUND} />
            <Text style={[styles.buttonText, styles.primaryButtonText]}>
              {isPickingFiles ? 'Picking...' : 'Select Files'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryButton]}
            onPress={handlePickImages}
            disabled={isPickingFiles}
          >
            <Icon name="photo-library" size={24} color={COLORS.PRIMARY} />
            <Text style={[styles.buttonText, styles.secondaryButtonText]}>
              {isPickingFiles ? 'Picking...' : 'Select Images'}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.fileListContainer}>
          <Text style={styles.sectionTitle}>
            Selected Files ({selectedFiles.length})
          </Text>
          
          {selectedFiles.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Icon name="cloud-upload" size={64} color={COLORS.GRAY} />
              <Text style={styles.emptyText}>No files selected</Text>
              <Text style={styles.emptySubtext}>
                Tap "Select Files" or "Select Images" to choose files to upload
              </Text>
            </View>
          ) : (
            <FlatList
              data={selectedFiles}
              renderItem={renderFileItem}
              keyExtractor={(item) => item.id}
              style={styles.fileList}
              showsVerticalScrollIndicator={false}
            />
          )}
        </View>

        {selectedFiles.length > 0 && (
          <View style={styles.footer}>
            <TouchableOpacity
              style={[styles.uploadButton]}
              onPress={handleUpload}
            >
              <Icon name="cloud-upload" size={24} color={COLORS.BACKGROUND} />
              <Text style={styles.uploadButtonText}>
                Upload {selectedFiles.length} File{selectedFiles.length > 1 ? 's' : ''}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SPACING.MD,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  closeButton: {
    padding: SPACING.SM,
  },
  title: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  placeholder: {
    width: 40,
  },
  pathContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.MD,
    backgroundColor: COLORS.SURFACE,
  },
  pathText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginLeft: SPACING.SM,
  },
  actionButtons: {
    flexDirection: 'row',
    padding: SPACING.MD,
    gap: SPACING.MD,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.MD,
    borderRadius: 8,
  },
  primaryButton: {
    backgroundColor: COLORS.PRIMARY,
  },
  secondaryButton: {
    backgroundColor: COLORS.BACKGROUND,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY,
  },
  buttonText: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '500',
    marginLeft: SPACING.SM,
  },
  primaryButtonText: {
    color: COLORS.BACKGROUND,
  },
  secondaryButtonText: {
    color: COLORS.PRIMARY,
  },
  fileListContainer: {
    flex: 1,
    padding: SPACING.MD,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: FONT_SIZES.LG,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.MD,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginTop: SPACING.SM,
    lineHeight: 20,
  },
  fileList: {
    flex: 1,
  },
  fileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.MD,
    backgroundColor: COLORS.SURFACE,
    borderRadius: 8,
    marginBottom: SPACING.SM,
  },
  fileInfo: {
    flex: 1,
    marginLeft: SPACING.MD,
  },
  fileName: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '500',
    color: COLORS.TEXT_PRIMARY,
  },
  fileSize: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.XS,
  },
  removeButton: {
    padding: SPACING.SM,
  },
  footer: {
    padding: SPACING.MD,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.SUCCESS,
    padding: SPACING.MD,
    borderRadius: 8,
  },
  uploadButtonText: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.BACKGROUND,
    marginLeft: SPACING.SM,
  },
  optionsContainer: {
    maxHeight: 200,
    paddingHorizontal: SPACING.MD,
    marginBottom: SPACING.MD,
  },
  optionSection: {
    marginBottom: SPACING.MD,
  },
  optionLabel: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  pickerContainer: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  picker: {
    height: 50,
    color: COLORS.TEXT_PRIMARY,
  },
});
