import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { StorageService, S3Service } from '../services';
import { AWSCredentials, StorageStats } from '../types';
import { COLORS, SPACING, FONT_SIZES } from '../utils/constants';
import { formatBytes } from '../utils/helpers';

interface HomeScreenProps {
  onNavigateToFiles: () => void;
  onNavigateToSettings: () => void;
  onLogout: () => void;
}

export const HomeScreen: React.FC<HomeScreenProps> = ({
  onNavigateToFiles,
  onNavigateToSettings,
  onLogout,
}) => {
  const [credentials, setCredentials] = useState<AWSCredentials | null>(null);
  const [stats, setStats] = useState<StorageStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    loadCredentials();
  }, []);

  const loadCredentials = async () => {
    try {
      const result = await StorageService.getCredentials();
      if (result.success && result.data) {
        setCredentials(result.data);
        await loadStats(result.data);
      }
    } catch (error) {
      console.error('Failed to load credentials:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadStats = async (creds: AWSCredentials) => {
    try {
      const s3Service = new S3Service();
      s3Service.initialize(creds);
      
      const result = await s3Service.listObjects('', undefined, 1000);
      
      if (result.success && result.data) {
        const objects = result.data.objects;
        const totalSize = objects.reduce((sum, obj) => sum + (obj.isFolder ? 0 : obj.size), 0);
        const fileCount = objects.filter(obj => !obj.isFolder).length;
        const folderCount = objects.filter(obj => obj.isFolder).length;
        
        setStats({
          totalObjects: objects.length,
          totalSize,
          folderCount,
          fileCount,
          lastUpdated: new Date(),
        });
      }
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  const handleRefresh = async () => {
    if (!credentials) return;
    
    setIsRefreshing(true);
    await loadStats(credentials);
    setIsRefreshing(false);
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout? Your credentials will be removed from this device.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await StorageService.clearCredentials();
            onLogout();
          },
        },
      ]
    );
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Icon name="cloud" size={64} color={COLORS.PRIMARY} />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.content}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
      }
    >
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Icon name="cloud" size={48} color={COLORS.PRIMARY} />
          <View style={styles.headerText}>
            <Text style={styles.title}>S3 Visualizer</Text>
            <Text style={styles.subtitle}>
              {credentials?.bucketName || 'Unknown Bucket'}
            </Text>
          </View>
        </View>
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Icon name="logout" size={24} color={COLORS.ERROR} />
        </TouchableOpacity>
      </View>

      {stats && (
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>Storage Overview</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Icon name="folder" size={32} color={COLORS.WARNING} />
              <Text style={styles.statNumber}>{stats.folderCount}</Text>
              <Text style={styles.statLabel}>Folders</Text>
            </View>
            <View style={styles.statCard}>
              <Icon name="insert-drive-file" size={32} color={COLORS.INFO} />
              <Text style={styles.statNumber}>{stats.fileCount}</Text>
              <Text style={styles.statLabel}>Files</Text>
            </View>
            <View style={styles.statCard}>
              <Icon name="storage" size={32} color={COLORS.SUCCESS} />
              <Text style={styles.statNumber}>{formatBytes(stats.totalSize)}</Text>
              <Text style={styles.statLabel}>Total Size</Text>
            </View>
            <View style={styles.statCard}>
              <Icon name="inventory" size={32} color={COLORS.SECONDARY} />
              <Text style={styles.statNumber}>{stats.totalObjects}</Text>
              <Text style={styles.statLabel}>Total Items</Text>
            </View>
          </View>
          <Text style={styles.lastUpdated}>
            Last updated: {stats.lastUpdated.toLocaleTimeString()}
          </Text>
        </View>
      )}

      <View style={styles.actionsContainer}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        
        <TouchableOpacity style={styles.actionButton} onPress={onNavigateToFiles}>
          <View style={styles.actionContent}>
            <Icon name="folder-open" size={32} color={COLORS.PRIMARY} />
            <View style={styles.actionText}>
              <Text style={styles.actionTitle}>Browse Files</Text>
              <Text style={styles.actionSubtitle}>
                View and manage your S3 objects
              </Text>
            </View>
            <Icon name="chevron-right" size={24} color={COLORS.GRAY} />
          </View>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={onNavigateToSettings}>
          <View style={styles.actionContent}>
            <Icon name="settings" size={32} color={COLORS.SECONDARY} />
            <View style={styles.actionText}>
              <Text style={styles.actionTitle}>Settings</Text>
              <Text style={styles.actionSubtitle}>
                Configure app preferences
              </Text>
            </View>
            <Icon name="chevron-right" size={24} color={COLORS.GRAY} />
          </View>
        </TouchableOpacity>
      </View>

      <View style={styles.infoContainer}>
        <Text style={styles.sectionTitle}>Connection Info</Text>
        <View style={styles.infoCard}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Region:</Text>
            <Text style={styles.infoValue}>{credentials?.region}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Bucket:</Text>
            <Text style={styles.infoValue}>{credentials?.bucketName}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Access Key:</Text>
            <Text style={styles.infoValue}>
              {credentials?.accessKeyId.substring(0, 8)}...
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  content: {
    padding: SPACING.LG,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.LG,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.MD,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: SPACING.XL,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerText: {
    marginLeft: SPACING.MD,
    flex: 1,
  },
  title: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  subtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.XS,
  },
  logoutButton: {
    padding: SPACING.SM,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  statsContainer: {
    marginBottom: SPACING.XL,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%',
    backgroundColor: COLORS.SURFACE,
    padding: SPACING.MD,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: SPACING.MD,
  },
  statNumber: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginTop: SPACING.SM,
  },
  statLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.XS,
  },
  lastUpdated: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginTop: SPACING.SM,
  },
  actionsContainer: {
    marginBottom: SPACING.XL,
  },
  actionButton: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    marginBottom: SPACING.MD,
  },
  actionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.MD,
  },
  actionText: {
    flex: 1,
    marginLeft: SPACING.MD,
  },
  actionTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  actionSubtitle: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.XS,
  },
  infoContainer: {
    marginBottom: SPACING.XL,
  },
  infoCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: SPACING.MD,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  infoLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  infoValue: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '500',
    color: COLORS.TEXT_PRIMARY,
  },
});
