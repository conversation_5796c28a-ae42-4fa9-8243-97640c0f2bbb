import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { StorageService, S3Service } from '../services';
import { AWSCredentials } from '../types';
import { AWS_REGIONS, COLORS, SPACING, FONT_SIZES } from '../utils/constants';
import { validateAWSCredentials } from '../utils/helpers';

interface SetupScreenProps {
  onSetupComplete: () => void;
}

export const SetupScreen: React.FC<SetupScreenProps> = ({ onSetupComplete }) => {
  const [credentials, setCredentials] = useState<AWSCredentials>({
    accessKeyId: '',
    secretAccessKey: '',
    region: 'us-east-1',
    bucketName: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showSecretKey, setShowSecretKey] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);

  const handleInputChange = (field: keyof AWSCredentials, value: string) => {
    setCredentials(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const testConnection = async () => {
    const validation = validateAWSCredentials(credentials);
    if (!validation.isValid) {
      Alert.alert('Validation Error', validation.errors.join('\n'));
      return;
    }

    setIsTestingConnection(true);
    
    try {
      const s3Service = new S3Service();
      s3Service.initialize(credentials);
      
      const result = await s3Service.testConnection();
      
      if (result.success && result.data?.success) {
        Alert.alert('Success', 'Connection test successful! Your credentials are valid.');
      } else {
        Alert.alert('Connection Failed', result.data?.message || 'Unknown error occurred');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to test connection. Please check your credentials.');
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleSave = async () => {
    const validation = validateAWSCredentials(credentials);
    if (!validation.isValid) {
      Alert.alert('Validation Error', validation.errors.join('\n'));
      return;
    }

    setIsLoading(true);
    
    try {
      // Test connection first
      const s3Service = new S3Service();
      s3Service.initialize(credentials);
      
      const connectionResult = await s3Service.testConnection();
      
      if (!connectionResult.success || !connectionResult.data?.success) {
        Alert.alert(
          'Connection Failed',
          connectionResult.data?.message || 'Please verify your credentials and try again.'
        );
        setIsLoading(false);
        return;
      }

      // Save credentials if connection is successful
      const saveResult = await StorageService.saveCredentials(credentials);
      
      if (saveResult.success) {
        Alert.alert('Success', 'Credentials saved successfully!', [
          { text: 'OK', onPress: onSetupComplete }
        ]);
      } else {
        Alert.alert('Error', 'Failed to save credentials. Please try again.');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Icon name="cloud" size={64} color={COLORS.PRIMARY} />
          <Text style={styles.title}>S3 Visualizer Setup</Text>
          <Text style={styles.subtitle}>
            Enter your AWS credentials to get started
          </Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Access Key ID *</Text>
            <TextInput
              style={styles.input}
              value={credentials.accessKeyId}
              onChangeText={(value) => handleInputChange('accessKeyId', value)}
              placeholder="Enter your AWS Access Key ID"
              autoCapitalize="characters"
              autoCorrect={false}
              secureTextEntry={false}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Secret Access Key *</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={[styles.input, styles.passwordInput]}
                value={credentials.secretAccessKey}
                onChangeText={(value) => handleInputChange('secretAccessKey', value)}
                placeholder="Enter your AWS Secret Access Key"
                secureTextEntry={!showSecretKey}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setShowSecretKey(!showSecretKey)}
              >
                <Icon
                  name={showSecretKey ? 'visibility-off' : 'visibility'}
                  size={24}
                  color={COLORS.GRAY}
                />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Region *</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={credentials.region}
                onValueChange={(value) => handleInputChange('region', value)}
                style={styles.picker}
              >
                {AWS_REGIONS.map((region) => (
                  <Picker.Item
                    key={region.value}
                    label={region.label}
                    value={region.value}
                  />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Bucket Name *</Text>
            <TextInput
              style={styles.input}
              value={credentials.bucketName}
              onChangeText={(value) => handleInputChange('bucketName', value)}
              placeholder="Enter your S3 bucket name"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <TouchableOpacity
            style={[styles.button, styles.testButton]}
            onPress={testConnection}
            disabled={isTestingConnection}
          >
            {isTestingConnection ? (
              <ActivityIndicator color={COLORS.PRIMARY} />
            ) : (
              <>
                <Icon name="wifi" size={20} color={COLORS.PRIMARY} />
                <Text style={[styles.buttonText, styles.testButtonText]}>
                  Test Connection
                </Text>
              </>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.saveButton]}
            onPress={handleSave}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color={COLORS.BACKGROUND} />
            ) : (
              <>
                <Icon name="save" size={20} color={COLORS.BACKGROUND} />
                <Text style={[styles.buttonText, styles.saveButtonText]}>
                  Save & Continue
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Your credentials are stored securely on your device using biometric authentication.
          </Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollContent: {
    flexGrow: 1,
    padding: SPACING.LG,
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.XXL,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginTop: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  subtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  form: {
    flex: 1,
  },
  inputGroup: {
    marginBottom: SPACING.LG,
  },
  label: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  input: {
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 8,
    padding: SPACING.MD,
    fontSize: FONT_SIZES.MD,
    backgroundColor: COLORS.BACKGROUND,
  },
  passwordContainer: {
    position: 'relative',
  },
  passwordInput: {
    paddingRight: 50,
  },
  eyeButton: {
    position: 'absolute',
    right: SPACING.MD,
    top: SPACING.MD,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 8,
    backgroundColor: COLORS.BACKGROUND,
  },
  picker: {
    height: 50,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.MD,
    borderRadius: 8,
    marginBottom: SPACING.MD,
  },
  testButton: {
    backgroundColor: COLORS.BACKGROUND,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY,
  },
  saveButton: {
    backgroundColor: COLORS.PRIMARY,
  },
  buttonText: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    marginLeft: SPACING.SM,
  },
  testButtonText: {
    color: COLORS.PRIMARY,
  },
  saveButtonText: {
    color: COLORS.BACKGROUND,
  },
  footer: {
    marginTop: SPACING.XL,
    alignItems: 'center',
  },
  footerText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 20,
  },
});
