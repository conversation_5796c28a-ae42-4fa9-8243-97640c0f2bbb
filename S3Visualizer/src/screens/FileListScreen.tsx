import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  Alert,
  TextInput,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { StorageService, S3Service, FileService } from '../services';
import { AWSCredentials, S3Object, AppSettings } from '../types';
import { COLORS, SPACING, FONT_SIZES } from '../utils/constants';
import { sortObjects, filterObjects, getBreadcrumbs, getFileIcon } from '../utils/helpers';

interface FileListScreenProps {
  path?: string;
  folderName?: string;
  onNavigateBack: () => void;
  onNavigateToFolder: (path: string, folderName: string) => void;
  onFileSelect: (file: S3Object) => void;
}

export const FileListScreen: React.FC<FileListScreenProps> = ({
  path = '',
  folderName = 'Root',
  onNavigateBack,
  onNavigateToFolder,
  onFileSelect,
}) => {
  const [objects, setObjects] = useState<S3Object[]>([]);
  const [filteredObjects, setFilteredObjects] = useState<S3Object[]>([]);
  const [credentials, setCredentials] = useState<AWSCredentials | null>(null);
  const [settings, setSettings] = useState<AppSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  useEffect(() => {
    loadData();
  }, [path]);

  useEffect(() => {
    if (settings) {
      const sorted = sortObjects(objects, settings);
      const filtered = searchQuery 
        ? filterObjects(sorted, { query: searchQuery })
        : sorted;
      setFilteredObjects(filtered);
    }
  }, [objects, settings, searchQuery]);

  const loadData = async () => {
    try {
      const [credResult, settingsResult] = await Promise.all([
        StorageService.getCredentials(),
        StorageService.getSettings(),
      ]);

      if (credResult.success && credResult.data) {
        setCredentials(credResult.data);
        await loadObjects(credResult.data);
      }

      if (settingsResult.success && settingsResult.data) {
        setSettings(settingsResult.data);
      }
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadObjects = async (creds: AWSCredentials) => {
    try {
      const s3Service = new S3Service();
      s3Service.initialize(creds);
      
      const result = await s3Service.listObjects(path);
      
      if (result.success && result.data) {
        setObjects(result.data.objects);
      }
    } catch (error) {
      console.error('Failed to load objects:', error);
      Alert.alert('Error', 'Failed to load files. Please try again.');
    }
  };

  const handleRefresh = async () => {
    if (!credentials) return;
    
    setIsRefreshing(true);
    await loadObjects(credentials);
    setIsRefreshing(false);
  };

  const handleItemPress = (item: S3Object) => {
    if (isSelectionMode) {
      toggleSelection(item.key);
    } else if (item.isFolder) {
      onNavigateToFolder(item.path, item.name);
    } else {
      onFileSelect(item);
    }
  };

  const handleItemLongPress = (item: S3Object) => {
    if (!isSelectionMode) {
      setIsSelectionMode(true);
      setSelectedItems(new Set([item.key]));
    }
  };

  const toggleSelection = (key: string) => {
    const newSelection = new Set(selectedItems);
    if (newSelection.has(key)) {
      newSelection.delete(key);
    } else {
      newSelection.add(key);
    }
    setSelectedItems(newSelection);
    
    if (newSelection.size === 0) {
      setIsSelectionMode(false);
    }
  };

  const handleDeleteSelected = () => {
    const selectedCount = selectedItems.size;
    Alert.alert(
      'Delete Items',
      `Are you sure you want to delete ${selectedCount} item(s)? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: deleteSelectedItems,
        },
      ]
    );
  };

  const deleteSelectedItems = async () => {
    if (!credentials) return;

    try {
      const s3Service = new S3Service();
      s3Service.initialize(credentials);
      
      const keys = Array.from(selectedItems);
      const result = await s3Service.deleteMultipleObjects(keys);
      
      if (result.success && result.data) {
        const { deleted, failed } = result.data;
        
        if (failed.length > 0) {
          Alert.alert('Partial Success', `${deleted.length} items deleted, ${failed.length} failed.`);
        } else {
          Alert.alert('Success', `${deleted.length} items deleted successfully.`);
        }
        
        // Refresh the list
        await loadObjects(credentials);
        setIsSelectionMode(false);
        setSelectedItems(new Set());
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to delete items. Please try again.');
    }
  };

  const renderFileItem = ({ item }: { item: S3Object }) => {
    const isSelected = selectedItems.has(item.key);
    const iconName = getFileIcon(item.name, item.isFolder);

    return (
      <TouchableOpacity
        style={[styles.fileItem, isSelected && styles.selectedItem]}
        onPress={() => handleItemPress(item)}
        onLongPress={() => handleItemLongPress(item)}
      >
        <View style={styles.fileIcon}>
          <Icon name={iconName} size={32} color={item.isFolder ? COLORS.WARNING : COLORS.INFO} />
        </View>
        <View style={styles.fileInfo}>
          <Text style={styles.fileName} numberOfLines={1}>
            {item.name}
          </Text>
          <Text style={styles.fileDetails}>
            {item.isFolder 
              ? 'Folder' 
              : `${FileService.formatFileSize(item.size)} • ${FileService.formatDate(item.lastModified)}`
            }
          </Text>
        </View>
        {isSelectionMode && (
          <View style={styles.selectionIndicator}>
            <Icon
              name={isSelected ? 'check-circle' : 'radio-button-unchecked'}
              size={24}
              color={isSelected ? COLORS.PRIMARY : COLORS.GRAY}
            />
          </View>
        )}
        {!isSelectionMode && !item.isFolder && (
          <TouchableOpacity style={styles.moreButton}>
            <Icon name="more-vert" size={24} color={COLORS.GRAY} />
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    );
  };

  const breadcrumbs = getBreadcrumbs(path);

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={onNavigateBack}>
          <Icon name="arrow-back" size={24} color={COLORS.TEXT_PRIMARY} />
        </TouchableOpacity>
        <View style={styles.headerTitle}>
          <Text style={styles.title}>{folderName}</Text>
          {breadcrumbs.length > 1 && (
            <Text style={styles.breadcrumb}>
              {breadcrumbs.map(b => b.name).join(' / ')}
            </Text>
          )}
        </View>
        {isSelectionMode && (
          <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteSelected}>
            <Icon name="delete" size={24} color={COLORS.ERROR} />
          </TouchableOpacity>
        )}
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color={COLORS.GRAY} style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search files and folders..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor={COLORS.GRAY}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Icon name="clear" size={20} color={COLORS.GRAY} />
          </TouchableOpacity>
        )}
      </View>

      {/* File List */}
      <FlatList
        data={filteredObjects}
        renderItem={renderFileItem}
        keyExtractor={(item) => item.key}
        style={styles.fileList}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="folder-open" size={64} color={COLORS.GRAY} />
            <Text style={styles.emptyText}>
              {searchQuery ? 'No files match your search' : 'This folder is empty'}
            </Text>
          </View>
        }
      />

      {/* Selection Mode Footer */}
      {isSelectionMode && (
        <View style={styles.selectionFooter}>
          <Text style={styles.selectionCount}>
            {selectedItems.size} item(s) selected
          </Text>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => {
              setIsSelectionMode(false);
              setSelectedItems(new Set());
            }}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.MD,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  backButton: {
    padding: SPACING.SM,
    marginRight: SPACING.SM,
  },
  headerTitle: {
    flex: 1,
  },
  title: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  breadcrumb: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.XS,
  },
  deleteButton: {
    padding: SPACING.SM,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: SPACING.MD,
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
    backgroundColor: COLORS.SURFACE,
    borderRadius: 8,
  },
  searchIcon: {
    marginRight: SPACING.SM,
  },
  searchInput: {
    flex: 1,
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
  },
  fileList: {
    flex: 1,
  },
  fileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.MD,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  selectedItem: {
    backgroundColor: COLORS.LIGHT,
  },
  fileIcon: {
    marginRight: SPACING.MD,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '500',
    color: COLORS.TEXT_PRIMARY,
  },
  fileDetails: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.XS,
  },
  selectionIndicator: {
    marginLeft: SPACING.SM,
  },
  moreButton: {
    padding: SPACING.SM,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.XXL,
  },
  emptyText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.MD,
    textAlign: 'center',
  },
  selectionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MD,
    backgroundColor: COLORS.SURFACE,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  selectionCount: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
  },
  cancelButton: {
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
  },
  cancelButtonText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.PRIMARY,
    fontWeight: '500',
  },
});
