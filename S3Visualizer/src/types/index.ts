// Core TypeScript interfaces for S3 Visualizer

export interface AWSCredentials {
  accessKeyId: string;
  secretAccessKey: string;
  region: string;
  bucketName: string;
}

export interface S3Object {
  key: string;
  name: string;
  size: number;
  lastModified: Date;
  isFolder: boolean;
  path: string;
  etag?: string;
  contentType?: string;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
  key: string;
}

export interface DownloadProgress {
  loaded: number;
  total: number;
  percentage: number;
  key: string;
}

export interface FileOperation {
  id: string;
  type: 'upload' | 'download' | 'delete' | 'copy' | 'move';
  key: string;
  status: 'pending' | 'in-progress' | 'completed' | 'failed' | 'cancelled';
  progress?: UploadProgress | DownloadProgress;
  error?: string;
  startTime: Date;
  endTime?: Date;
}

export interface FolderStructure {
  name: string;
  path: string;
  children: (S3Object | FolderStructure)[];
  isExpanded?: boolean;
}

export interface SearchFilter {
  query: string;
  fileType?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  sizeRange?: {
    min: number;
    max: number;
  };
}

export interface AppSettings {
  viewMode: 'grid' | 'list';
  sortBy: 'name' | 'date' | 'size';
  sortOrder: 'asc' | 'desc';
  showHiddenFiles: boolean;
  autoRefresh: boolean;
  refreshInterval: number; // in seconds
  maxConcurrentUploads: number;
  chunkSize: number; // for multipart uploads
}

export interface ShareableLink {
  url: string;
  key: string;
  expiresAt: Date;
  isPublic: boolean;
}

export interface StorageStats {
  totalObjects: number;
  totalSize: number;
  folderCount: number;
  fileCount: number;
  lastUpdated: Date;
}

// Navigation types
export type RootStackParamList = {
  Setup: undefined;
  Home: undefined;
  FileList: {
    path?: string;
    folderName?: string;
  };
  Settings: undefined;
  FileDetails: {
    object: S3Object;
  };
};

// Error types
export interface S3Error {
  code: string;
  message: string;
  statusCode?: number;
  requestId?: string;
}

// Service response types
export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: S3Error;
}

export interface ListObjectsResponse {
  objects: S3Object[];
  folders: string[];
  continuationToken?: string;
  isTruncated: boolean;
}

export interface ConnectionTestResult {
  success: boolean;
  message: string;
  bucketExists?: boolean;
  hasPermissions?: boolean;
}
