import {
  S3Client,
  ListObjectsV2Command,
  GetObjectCommand,
  PutO<PERSON>Command,
  DeleteObjectCommand,
  CopyObjectCommand,
  HeadBucketCommand,
  HeadObjectCommand,
  CreateMultipartUploadCommand,
  UploadPartCommand,
  CompleteMultipartUploadCommand,
  AbortMultipartUploadCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import {
  AWSCredentials,
  S3Object,
  ServiceResponse,
  ListObjectsResponse,
  ConnectionTestResult,
  UploadProgress,
} from '../types';

/**
 * S3Service handles all AWS S3 operations
 */
export class S3Service {
  private s3Client: S3Client | null = null;
  private credentials: AWSCredentials | null = null;

  /**
   * Initialize S3 client with credentials
   */
  initialize(credentials: AWSCredentials): void {
    this.credentials = credentials;
    this.s3Client = new S3Client({
      region: credentials.region,
      credentials: {
        accessKeyId: credentials.accessKeyId,
        secretAccessKey: credentials.secretAccessKey,
      },
    });
  }

  /**
   * Test connection to AWS S3 and validate credentials
   */
  async testConnection(): Promise<ServiceResponse<ConnectionTestResult>> {
    if (!this.s3Client || !this.credentials) {
      return {
        success: false,
        error: {
          code: 'NOT_INITIALIZED',
          message: 'S3 service not initialized',
        },
      };
    }

    try {
      // Test bucket access
      const command = new HeadBucketCommand({
        Bucket: this.credentials.bucketName,
      });

      await this.s3Client.send(command);

      return {
        success: true,
        data: {
          success: true,
          message: 'Connection successful',
          bucketExists: true,
          hasPermissions: true,
        },
      };
    } catch (error: any) {
      console.error('Connection test failed:', error);

      let message = 'Connection failed';
      let bucketExists = false;
      let hasPermissions = false;

      if (error.name === 'NotFound') {
        message = 'Bucket not found';
      } else if (error.name === 'Forbidden') {
        message = 'Access denied - check your credentials and permissions';
        bucketExists = true;
      } else if (error.name === 'InvalidAccessKeyId') {
        message = 'Invalid Access Key ID';
      } else if (error.name === 'SignatureDoesNotMatch') {
        message = 'Invalid Secret Access Key';
      } else if (error.name === 'NetworkingError') {
        message = 'Network error - check your internet connection';
      }

      return {
        success: true,
        data: {
          success: false,
          message,
          bucketExists,
          hasPermissions,
        },
      };
    }
  }

  /**
   * List objects in S3 bucket with optional prefix
   */
  async listObjects(
    prefix: string = '',
    continuationToken?: string,
    maxKeys: number = 1000
  ): Promise<ServiceResponse<ListObjectsResponse>> {
    if (!this.s3Client || !this.credentials) {
      return {
        success: false,
        error: {
          code: 'NOT_INITIALIZED',
          message: 'S3 service not initialized',
        },
      };
    }

    try {
      const command = new ListObjectsV2Command({
        Bucket: this.credentials.bucketName,
        Prefix: prefix,
        Delimiter: '/',
        MaxKeys: maxKeys,
        ContinuationToken: continuationToken,
      });

      const response = await this.s3Client.send(command);

      const objects: S3Object[] = [];
      const folders: string[] = [];

      // Process files
      if (response.Contents) {
        for (const item of response.Contents) {
          if (item.Key && item.Key !== prefix) {
            const name = item.Key.split('/').pop() || item.Key;
            objects.push({
              key: item.Key,
              name,
              size: item.Size || 0,
              lastModified: item.LastModified || new Date(),
              isFolder: false,
              path: item.Key,
              etag: item.ETag,
            });
          }
        }
      }

      // Process folders (common prefixes)
      if (response.CommonPrefixes) {
        for (const commonPrefix of response.CommonPrefixes) {
          if (commonPrefix.Prefix) {
            const folderName = commonPrefix.Prefix.replace(prefix, '').replace('/', '');
            if (folderName) {
              folders.push(folderName);
              objects.push({
                key: commonPrefix.Prefix,
                name: folderName,
                size: 0,
                lastModified: new Date(),
                isFolder: true,
                path: commonPrefix.Prefix,
              });
            }
          }
        }
      }

      return {
        success: true,
        data: {
          objects,
          folders,
          continuationToken: response.NextContinuationToken,
          isTruncated: response.IsTruncated || false,
        },
      };
    } catch (error: any) {
      console.error('List objects failed:', error);
      return {
        success: false,
        error: {
          code: error.name || 'LIST_OBJECTS_ERROR',
          message: error.message || 'Failed to list objects',
          statusCode: error.$metadata?.httpStatusCode,
        },
      };
    }
  }

  /**
   * Upload file to S3
   */
  async uploadFile(
    file: { uri: string; name: string; type?: string },
    key: string,
    progressCallback?: (progress: UploadProgress) => void
  ): Promise<ServiceResponse<S3Object>> {
    if (!this.s3Client || !this.credentials) {
      return {
        success: false,
        error: {
          code: 'NOT_INITIALIZED',
          message: 'S3 service not initialized',
        },
      };
    }

    try {
      // Read file data
      const response = await fetch(file.uri);
      const blob = await response.blob();
      const buffer = await blob.arrayBuffer();
      const uint8Array = new Uint8Array(buffer);

      // For small files, use simple upload
      if (uint8Array.length < 100 * 1024 * 1024) { // 100MB
        const command = new PutObjectCommand({
          Bucket: this.credentials.bucketName,
          Key: key,
          Body: uint8Array,
          ContentType: file.type || 'application/octet-stream',
        });

        const result = await this.s3Client.send(command);

        // Simulate progress for simple upload
        if (progressCallback) {
          progressCallback({
            loaded: uint8Array.length,
            total: uint8Array.length,
            percentage: 100,
            key,
          });
        }

        return {
          success: true,
          data: {
            key,
            name: file.name,
            size: uint8Array.length,
            lastModified: new Date(),
            isFolder: false,
            path: key,
            etag: result.ETag,
          },
        };
      } else {
        // Use multipart upload for large files
        return await this.multipartUpload(uint8Array, key, file, progressCallback);
      }
    } catch (error: any) {
      console.error('Upload failed:', error);
      return {
        success: false,
        error: {
          code: error.name || 'UPLOAD_ERROR',
          message: error.message || 'Failed to upload file',
          statusCode: error.$metadata?.httpStatusCode,
        },
      };
    }
  }

  /**
   * Multipart upload for large files
   */
  private async multipartUpload(
    data: Uint8Array,
    key: string,
    file: { name: string; type?: string },
    progressCallback?: (progress: UploadProgress) => void
  ): Promise<ServiceResponse<S3Object>> {
    if (!this.s3Client || !this.credentials) {
      throw new Error('S3 service not initialized');
    }

    const chunkSize = 5 * 1024 * 1024; // 5MB chunks
    const chunks = Math.ceil(data.length / chunkSize);
    let uploadedBytes = 0;

    try {
      // Initialize multipart upload
      const createCommand = new CreateMultipartUploadCommand({
        Bucket: this.credentials.bucketName,
        Key: key,
        ContentType: file.type || 'application/octet-stream',
      });

      const createResponse = await this.s3Client.send(createCommand);
      const uploadId = createResponse.UploadId;

      if (!uploadId) {
        throw new Error('Failed to initialize multipart upload');
      }

      // Upload parts
      const parts = [];
      for (let i = 0; i < chunks; i++) {
        const start = i * chunkSize;
        const end = Math.min(start + chunkSize, data.length);
        const chunk = data.slice(start, end);

        const uploadPartCommand = new UploadPartCommand({
          Bucket: this.credentials.bucketName,
          Key: key,
          PartNumber: i + 1,
          UploadId: uploadId,
          Body: chunk,
        });

        const partResponse = await this.s3Client.send(uploadPartCommand);
        parts.push({
          ETag: partResponse.ETag,
          PartNumber: i + 1,
        });

        uploadedBytes += chunk.length;

        // Report progress
        if (progressCallback) {
          progressCallback({
            loaded: uploadedBytes,
            total: data.length,
            percentage: Math.round((uploadedBytes / data.length) * 100),
            key,
          });
        }
      }

      // Complete multipart upload
      const completeCommand = new CompleteMultipartUploadCommand({
        Bucket: this.credentials.bucketName,
        Key: key,
        UploadId: uploadId,
        MultipartUpload: {
          Parts: parts,
        },
      });

      const result = await this.s3Client.send(completeCommand);

      return {
        success: true,
        data: {
          key,
          name: file.name,
          size: data.length,
          lastModified: new Date(),
          isFolder: false,
          path: key,
          etag: result.ETag,
        },
      };
    } catch (error: any) {
      // Abort multipart upload on error
      if (this.s3Client && this.credentials) {
        try {
          const abortCommand = new AbortMultipartUploadCommand({
            Bucket: this.credentials.bucketName,
            Key: key,
            UploadId: createResponse?.UploadId,
          });
          await this.s3Client.send(abortCommand);
        } catch (abortError) {
          console.error('Failed to abort multipart upload:', abortError);
        }
      }

      throw error;
    }
  }

  /**
   * Generate signed download URL for a file
   */
  async downloadFile(key: string, expiresIn: number = 3600): Promise<ServiceResponse<string>> {
    if (!this.s3Client || !this.credentials) {
      return {
        success: false,
        error: {
          code: 'NOT_INITIALIZED',
          message: 'S3 service not initialized',
        },
      };
    }

    try {
      const command = new GetObjectCommand({
        Bucket: this.credentials.bucketName,
        Key: key,
      });

      const signedUrl = await getSignedUrl(this.s3Client, command, {
        expiresIn,
      });

      return {
        success: true,
        data: signedUrl,
      };
    } catch (error: any) {
      console.error('Download URL generation failed:', error);
      return {
        success: false,
        error: {
          code: error.name || 'DOWNLOAD_ERROR',
          message: error.message || 'Failed to generate download URL',
          statusCode: error.$metadata?.httpStatusCode,
        },
      };
    }
  }

  /**
   * Delete an object from S3
   */
  async deleteObject(key: string): Promise<ServiceResponse<boolean>> {
    if (!this.s3Client || !this.credentials) {
      return {
        success: false,
        error: {
          code: 'NOT_INITIALIZED',
          message: 'S3 service not initialized',
        },
      };
    }

    try {
      const command = new DeleteObjectCommand({
        Bucket: this.credentials.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);

      return {
        success: true,
        data: true,
      };
    } catch (error: any) {
      console.error('Delete failed:', error);
      return {
        success: false,
        error: {
          code: error.name || 'DELETE_ERROR',
          message: error.message || 'Failed to delete object',
          statusCode: error.$metadata?.httpStatusCode,
        },
      };
    }
  }

  /**
   * Copy an object within S3
   */
  async copyObject(sourceKey: string, destKey: string): Promise<ServiceResponse<S3Object>> {
    if (!this.s3Client || !this.credentials) {
      return {
        success: false,
        error: {
          code: 'NOT_INITIALIZED',
          message: 'S3 service not initialized',
        },
      };
    }

    try {
      const copySource = `${this.credentials.bucketName}/${sourceKey}`;

      const command = new CopyObjectCommand({
        Bucket: this.credentials.bucketName,
        CopySource: copySource,
        Key: destKey,
      });

      const result = await this.s3Client.send(command);

      // Get object info for the copied file
      const headCommand = new HeadObjectCommand({
        Bucket: this.credentials.bucketName,
        Key: destKey,
      });

      const headResult = await this.s3Client.send(headCommand);

      return {
        success: true,
        data: {
          key: destKey,
          name: destKey.split('/').pop() || destKey,
          size: headResult.ContentLength || 0,
          lastModified: headResult.LastModified || new Date(),
          isFolder: false,
          path: destKey,
          etag: result.CopyObjectResult?.ETag,
        },
      };
    } catch (error: any) {
      console.error('Copy failed:', error);
      return {
        success: false,
        error: {
          code: error.name || 'COPY_ERROR',
          message: error.message || 'Failed to copy object',
          statusCode: error.$metadata?.httpStatusCode,
        },
      };
    }
  }

  /**
   * Move an object (copy then delete)
   */
  async moveObject(sourceKey: string, destKey: string): Promise<ServiceResponse<S3Object>> {
    try {
      // First copy the object
      const copyResult = await this.copyObject(sourceKey, destKey);

      if (!copyResult.success) {
        return copyResult;
      }

      // Then delete the original
      const deleteResult = await this.deleteObject(sourceKey);

      if (!deleteResult.success) {
        // If delete fails, try to clean up the copy
        await this.deleteObject(destKey);
        return {
          success: false,
          error: deleteResult.error,
        };
      }

      return copyResult;
    } catch (error: any) {
      console.error('Move failed:', error);
      return {
        success: false,
        error: {
          code: 'MOVE_ERROR',
          message: 'Failed to move object',
        },
      };
    }
  }

  /**
   * Create a folder (by creating a placeholder object)
   */
  async createFolder(path: string): Promise<ServiceResponse<S3Object>> {
    if (!this.s3Client || !this.credentials) {
      return {
        success: false,
        error: {
          code: 'NOT_INITIALIZED',
          message: 'S3 service not initialized',
        },
      };
    }

    try {
      // Ensure path ends with /
      const folderKey = path.endsWith('/') ? path : `${path}/`;

      const command = new PutObjectCommand({
        Bucket: this.credentials.bucketName,
        Key: folderKey,
        Body: '',
        ContentType: 'application/x-directory',
      });

      await this.s3Client.send(command);

      return {
        success: true,
        data: {
          key: folderKey,
          name: path.split('/').filter(Boolean).pop() || path,
          size: 0,
          lastModified: new Date(),
          isFolder: true,
          path: folderKey,
        },
      };
    } catch (error: any) {
      console.error('Create folder failed:', error);
      return {
        success: false,
        error: {
          code: error.name || 'CREATE_FOLDER_ERROR',
          message: error.message || 'Failed to create folder',
          statusCode: error.$metadata?.httpStatusCode,
        },
      };
    }
  }

  /**
   * Generate a shareable presigned URL
   */
  async getShareableUrl(key: string, expiresIn: number = 3600): Promise<ServiceResponse<string>> {
    return this.downloadFile(key, expiresIn);
  }

  /**
   * Get object metadata
   */
  async getObjectInfo(key: string): Promise<ServiceResponse<S3Object>> {
    if (!this.s3Client || !this.credentials) {
      return {
        success: false,
        error: {
          code: 'NOT_INITIALIZED',
          message: 'S3 service not initialized',
        },
      };
    }

    try {
      const command = new HeadObjectCommand({
        Bucket: this.credentials.bucketName,
        Key: key,
      });

      const result = await this.s3Client.send(command);

      return {
        success: true,
        data: {
          key,
          name: key.split('/').pop() || key,
          size: result.ContentLength || 0,
          lastModified: result.LastModified || new Date(),
          isFolder: false,
          path: key,
          etag: result.ETag,
          contentType: result.ContentType,
        },
      };
    } catch (error: any) {
      console.error('Get object info failed:', error);
      return {
        success: false,
        error: {
          code: error.name || 'GET_INFO_ERROR',
          message: error.message || 'Failed to get object information',
          statusCode: error.$metadata?.httpStatusCode,
        },
      };
    }
  }

  /**
   * Delete multiple objects
   */
  async deleteMultipleObjects(keys: string[]): Promise<ServiceResponse<{ deleted: string[]; failed: string[] }>> {
    const deleted: string[] = [];
    const failed: string[] = [];

    for (const key of keys) {
      const result = await this.deleteObject(key);
      if (result.success) {
        deleted.push(key);
      } else {
        failed.push(key);
      }
    }

    return {
      success: true,
      data: { deleted, failed },
    };
  }

  /**
   * Check if object exists
   */
  async objectExists(key: string): Promise<ServiceResponse<boolean>> {
    const result = await this.getObjectInfo(key);
    return {
      success: true,
      data: result.success,
    };
  }
}
