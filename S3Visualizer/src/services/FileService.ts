import DocumentPicker, { DocumentPickerResponse } from 'react-native-document-picker';
import { Platform } from 'react-native';
import { ServiceResponse, S3Object } from '../types';

/**
 * FileService handles local file operations and utilities
 */
export class FileService {
  /**
   * Pick files from device storage
   */
  static async pickFiles(allowMultiple: boolean = true): Promise<ServiceResponse<DocumentPickerResponse[]>> {
    try {
      const result = await DocumentPicker.pick({
        type: [DocumentPicker.types.allFiles],
        allowMultiSelection: allowMultiple,
      });

      return {
        success: true,
        data: Array.isArray(result) ? result : [result],
      };
    } catch (error) {
      if (DocumentPicker.isCancel(error)) {
        return {
          success: true,
          data: [],
        };
      }

      console.error('File picker error:', error);
      return {
        success: false,
        error: {
          code: 'FILE_PICKER_ERROR',
          message: 'Failed to pick files',
        },
      };
    }
  }

  /**
   * Pick images from device storage
   */
  static async pickImages(allowMultiple: boolean = true): Promise<ServiceResponse<DocumentPickerResponse[]>> {
    try {
      const result = await DocumentPicker.pick({
        type: [DocumentPicker.types.images],
        allowMultiSelection: allowMultiple,
      });

      return {
        success: true,
        data: Array.isArray(result) ? result : [result],
      };
    } catch (error) {
      if (DocumentPicker.isCancel(error)) {
        return {
          success: true,
          data: [],
        };
      }

      console.error('Image picker error:', error);
      return {
        success: false,
        error: {
          code: 'IMAGE_PICKER_ERROR',
          message: 'Failed to pick images',
        },
      };
    }
  }

  /**
   * Get file extension from filename
   */
  static getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex !== -1 ? filename.substring(lastDotIndex + 1).toLowerCase() : '';
  }

  /**
   * Get file type category based on extension
   */
  static getFileType(filename: string): 'image' | 'video' | 'audio' | 'document' | 'archive' | 'other' {
    const extension = this.getFileExtension(filename);
    
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'];
    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp'];
    const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'];
    const documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'csv'];
    const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'];

    if (imageExtensions.includes(extension)) return 'image';
    if (videoExtensions.includes(extension)) return 'video';
    if (audioExtensions.includes(extension)) return 'audio';
    if (documentExtensions.includes(extension)) return 'document';
    if (archiveExtensions.includes(extension)) return 'archive';
    
    return 'other';
  }

  /**
   * Format file size in human readable format
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Format date in human readable format
   */
  static formatDate(date: Date): string {
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return 'Today';
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  /**
   * Get MIME type from file extension
   */
  static getMimeType(filename: string): string {
    const extension = this.getFileExtension(filename);
    
    const mimeTypes: { [key: string]: string } = {
      // Images
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'bmp': 'image/bmp',
      'webp': 'image/webp',
      'svg': 'image/svg+xml',
      'ico': 'image/x-icon',
      
      // Videos
      'mp4': 'video/mp4',
      'avi': 'video/x-msvideo',
      'mov': 'video/quicktime',
      'wmv': 'video/x-ms-wmv',
      'flv': 'video/x-flv',
      'webm': 'video/webm',
      'mkv': 'video/x-matroska',
      
      // Audio
      'mp3': 'audio/mpeg',
      'wav': 'audio/wav',
      'flac': 'audio/flac',
      'aac': 'audio/aac',
      'ogg': 'audio/ogg',
      'wma': 'audio/x-ms-wma',
      'm4a': 'audio/mp4',
      
      // Documents
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'xls': 'application/vnd.ms-excel',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'ppt': 'application/vnd.ms-powerpoint',
      'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'txt': 'text/plain',
      'rtf': 'application/rtf',
      'csv': 'text/csv',
      
      // Archives
      'zip': 'application/zip',
      'rar': 'application/vnd.rar',
      '7z': 'application/x-7z-compressed',
      'tar': 'application/x-tar',
      'gz': 'application/gzip',
      'bz2': 'application/x-bzip2',
    };

    return mimeTypes[extension] || 'application/octet-stream';
  }

  /**
   * Validate file name for S3 compatibility
   */
  static validateFileName(filename: string): { isValid: boolean; message?: string } {
    // S3 key naming rules
    if (filename.length === 0) {
      return { isValid: false, message: 'Filename cannot be empty' };
    }

    if (filename.length > 1024) {
      return { isValid: false, message: 'Filename too long (max 1024 characters)' };
    }

    // Check for invalid characters
    const invalidChars = /[<>:"|?*\x00-\x1f]/;
    if (invalidChars.test(filename)) {
      return { isValid: false, message: 'Filename contains invalid characters' };
    }

    // Check for reserved names (Windows)
    const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
    const nameWithoutExtension = filename.split('.')[0].toUpperCase();
    if (reservedNames.includes(nameWithoutExtension)) {
      return { isValid: false, message: 'Filename uses a reserved name' };
    }

    return { isValid: true };
  }

  /**
   * Generate a safe S3 key from a file path
   */
  static generateS3Key(folderPath: string, filename: string): string {
    // Remove leading/trailing slashes and normalize
    const normalizedPath = folderPath.replace(/^\/+|\/+$/g, '').replace(/\/+/g, '/');
    const normalizedFilename = filename.replace(/[<>:"|?*\x00-\x1f]/g, '_');
    
    if (normalizedPath) {
      return `${normalizedPath}/${normalizedFilename}`;
    }
    
    return normalizedFilename;
  }

  /**
   * Extract folder path and filename from S3 key
   */
  static parseS3Key(key: string): { folderPath: string; filename: string } {
    const lastSlashIndex = key.lastIndexOf('/');
    
    if (lastSlashIndex === -1) {
      return { folderPath: '', filename: key };
    }
    
    return {
      folderPath: key.substring(0, lastSlashIndex),
      filename: key.substring(lastSlashIndex + 1),
    };
  }
}
