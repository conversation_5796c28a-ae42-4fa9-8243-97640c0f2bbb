import * as Keychain from 'react-native-keychain';
import { AWSCredentials, AppSettings, ServiceResponse } from '../types';

/**
 * StorageService handles secure storage of AWS credentials and app settings
 * using react-native-keychain for secure credential storage
 */
export class StorageService {
  private static readonly CREDENTIALS_KEY = 'aws_credentials';
  private static readonly SETTINGS_KEY = 'app_settings';
  private static readonly SERVICE_NAME = 'S3Visualizer';

  /**
   * Save AWS credentials securely using Keychain
   */
  static async saveCredentials(credentials: AWSCredentials): Promise<ServiceResponse<boolean>> {
    try {
      const credentialsString = JSON.stringify(credentials);
      
      await Keychain.setInternetCredentials(
        this.CREDENTIALS_KEY,
        credentials.accessKeyId,
        credentialsString,
        {
          accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_CURRENT_SET_OR_DEVICE_PASSCODE,
          accessible: Keychain.ACCESSIBLE.WHEN_UNLOCKED_THIS_DEVICE_ONLY,
          service: this.SERVICE_NAME,
        }
      );

      return {
        success: true,
        data: true,
      };
    } catch (error) {
      console.error('Failed to save credentials:', error);
      return {
        success: false,
        error: {
          code: 'STORAGE_ERROR',
          message: 'Failed to save credentials securely',
        },
      };
    }
  }

  /**
   * Retrieve AWS credentials from secure storage
   */
  static async getCredentials(): Promise<ServiceResponse<AWSCredentials | null>> {
    try {
      const credentials = await Keychain.getInternetCredentials(this.CREDENTIALS_KEY);
      
      if (credentials && credentials.password) {
        const parsedCredentials: AWSCredentials = JSON.parse(credentials.password);
        return {
          success: true,
          data: parsedCredentials,
        };
      }

      return {
        success: true,
        data: null,
      };
    } catch (error) {
      console.error('Failed to retrieve credentials:', error);
      return {
        success: false,
        error: {
          code: 'STORAGE_ERROR',
          message: 'Failed to retrieve credentials',
        },
      };
    }
  }

  /**
   * Check if credentials exist in secure storage
   */
  static async hasCredentials(): Promise<boolean> {
    try {
      const credentials = await Keychain.getInternetCredentials(this.CREDENTIALS_KEY);
      return credentials && credentials.password !== '';
    } catch (error) {
      console.error('Failed to check credentials:', error);
      return false;
    }
  }

  /**
   * Clear stored AWS credentials
   */
  static async clearCredentials(): Promise<ServiceResponse<boolean>> {
    try {
      await Keychain.resetInternetCredentials(this.CREDENTIALS_KEY);
      return {
        success: true,
        data: true,
      };
    } catch (error) {
      console.error('Failed to clear credentials:', error);
      return {
        success: false,
        error: {
          code: 'STORAGE_ERROR',
          message: 'Failed to clear credentials',
        },
      };
    }
  }

  /**
   * Save app settings to local storage
   */
  static async saveSettings(settings: AppSettings): Promise<ServiceResponse<boolean>> {
    try {
      const settingsString = JSON.stringify(settings);
      
      await Keychain.setInternetCredentials(
        this.SETTINGS_KEY,
        'settings',
        settingsString,
        {
          accessible: Keychain.ACCESSIBLE.WHEN_UNLOCKED_THIS_DEVICE_ONLY,
          service: this.SERVICE_NAME,
        }
      );

      return {
        success: true,
        data: true,
      };
    } catch (error) {
      console.error('Failed to save settings:', error);
      return {
        success: false,
        error: {
          code: 'STORAGE_ERROR',
          message: 'Failed to save app settings',
        },
      };
    }
  }

  /**
   * Retrieve app settings from local storage
   */
  static async getSettings(): Promise<ServiceResponse<AppSettings>> {
    try {
      const credentials = await Keychain.getInternetCredentials(this.SETTINGS_KEY);
      
      if (credentials && credentials.password) {
        const settings: AppSettings = JSON.parse(credentials.password);
        return {
          success: true,
          data: settings,
        };
      }

      // Return default settings if none exist
      const defaultSettings: AppSettings = {
        viewMode: 'list',
        sortBy: 'name',
        sortOrder: 'asc',
        showHiddenFiles: false,
        autoRefresh: false,
        refreshInterval: 30,
        maxConcurrentUploads: 3,
        chunkSize: 5 * 1024 * 1024, // 5MB
      };

      return {
        success: true,
        data: defaultSettings,
      };
    } catch (error) {
      console.error('Failed to retrieve settings:', error);
      return {
        success: false,
        error: {
          code: 'STORAGE_ERROR',
          message: 'Failed to retrieve app settings',
        },
      };
    }
  }

  /**
   * Clear all stored data (credentials and settings)
   */
  static async clearAllData(): Promise<ServiceResponse<boolean>> {
    try {
      await Promise.all([
        Keychain.resetInternetCredentials(this.CREDENTIALS_KEY),
        Keychain.resetInternetCredentials(this.SETTINGS_KEY),
      ]);

      return {
        success: true,
        data: true,
      };
    } catch (error) {
      console.error('Failed to clear all data:', error);
      return {
        success: false,
        error: {
          code: 'STORAGE_ERROR',
          message: 'Failed to clear all stored data',
        },
      };
    }
  }
}
