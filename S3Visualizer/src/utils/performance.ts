/**
 * Performance monitoring utilities for the S3 Visualizer app
 */

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

interface MemoryInfo {
  used: number;
  total: number;
  percentage: number;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private completedMetrics: PerformanceMetric[] = [];
  private maxStoredMetrics = 100;

  /**
   * Start measuring performance for an operation
   */
  startMeasure(name: string, metadata?: Record<string, any>): void {
    const metric: PerformanceMetric = {
      name,
      startTime: Date.now(),
      metadata,
    };
    
    this.metrics.set(name, metric);
  }

  /**
   * End measuring performance for an operation
   */
  endMeasure(name: string): number | null {
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric '${name}' not found`);
      return null;
    }

    const endTime = Date.now();
    const duration = endTime - metric.startTime;

    const completedMetric: PerformanceMetric = {
      ...metric,
      endTime,
      duration,
    };

    // Store completed metric
    this.completedMetrics.push(completedMetric);
    
    // Remove from active metrics
    this.metrics.delete(name);

    // Limit stored metrics to prevent memory leaks
    if (this.completedMetrics.length > this.maxStoredMetrics) {
      this.completedMetrics.shift();
    }

    console.log(`Performance: ${name} took ${duration}ms`);
    return duration;
  }

  /**
   * Measure an async operation
   */
  async measureAsync<T>(
    name: string,
    operation: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    this.startMeasure(name, metadata);
    try {
      const result = await operation();
      this.endMeasure(name);
      return result;
    } catch (error) {
      this.endMeasure(name);
      throw error;
    }
  }

  /**
   * Measure a synchronous operation
   */
  measureSync<T>(
    name: string,
    operation: () => T,
    metadata?: Record<string, any>
  ): T {
    this.startMeasure(name, metadata);
    try {
      const result = operation();
      this.endMeasure(name);
      return result;
    } catch (error) {
      this.endMeasure(name);
      throw error;
    }
  }

  /**
   * Get performance statistics
   */
  getStats(operationName?: string): {
    count: number;
    averageDuration: number;
    minDuration: number;
    maxDuration: number;
    totalDuration: number;
  } | null {
    const filteredMetrics = operationName
      ? this.completedMetrics.filter(m => m.name === operationName)
      : this.completedMetrics;

    if (filteredMetrics.length === 0) {
      return null;
    }

    const durations = filteredMetrics
      .map(m => m.duration!)
      .filter(d => d !== undefined);

    return {
      count: durations.length,
      averageDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      totalDuration: durations.reduce((sum, d) => sum + d, 0),
    };
  }

  /**
   * Get all completed metrics
   */
  getAllMetrics(): PerformanceMetric[] {
    return [...this.completedMetrics];
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics.clear();
    this.completedMetrics = [];
  }

  /**
   * Get memory usage information
   */
  getMemoryInfo(): MemoryInfo | null {
    // Note: React Native doesn't have direct access to memory info
    // This would need to be implemented with native modules
    // For now, return null
    return null;
  }

  /**
   * Log performance summary
   */
  logSummary(): void {
    const operations = [...new Set(this.completedMetrics.map(m => m.name))];
    
    console.log('=== Performance Summary ===');
    operations.forEach(operation => {
      const stats = this.getStats(operation);
      if (stats) {
        console.log(`${operation}:`);
        console.log(`  Count: ${stats.count}`);
        console.log(`  Average: ${stats.averageDuration.toFixed(2)}ms`);
        console.log(`  Min: ${stats.minDuration}ms`);
        console.log(`  Max: ${stats.maxDuration}ms`);
        console.log(`  Total: ${stats.totalDuration}ms`);
      }
    });
    console.log('========================');
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * Decorator for measuring method performance
 */
export function measurePerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const measureName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      return performanceMonitor.measureAsync(measureName, () => originalMethod.apply(this, args));
    };

    return descriptor;
  };
}

/**
 * Throttle function calls to improve performance
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return function (this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Debounce function calls to improve performance
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null;
  
  return function (this: any, ...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func.apply(this, args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func.apply(this, args);
  };
}

/**
 * Memoization for expensive computations
 */
export function memoize<T extends (...args: any[]) => any>(
  func: T,
  getKey?: (...args: Parameters<T>) => string
): T {
  const cache = new Map<string, ReturnType<T>>();
  
  return ((...args: Parameters<T>): ReturnType<T> => {
    const key = getKey ? getKey(...args) : JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key)!;
    }
    
    const result = func(...args);
    cache.set(key, result);
    
    return result;
  }) as T;
}

/**
 * Batch operations to reduce overhead
 */
export class BatchProcessor<T> {
  private batch: T[] = [];
  private timer: NodeJS.Timeout | null = null;

  constructor(
    private processor: (items: T[]) => Promise<void> | void,
    private batchSize: number = 10,
    private maxWaitTime: number = 1000
  ) {}

  add(item: T): void {
    this.batch.push(item);

    if (this.batch.length >= this.batchSize) {
      this.flush();
    } else if (!this.timer) {
      this.timer = setTimeout(() => this.flush(), this.maxWaitTime);
    }
  }

  async flush(): Promise<void> {
    if (this.batch.length === 0) return;

    const items = [...this.batch];
    this.batch = [];

    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }

    try {
      await this.processor(items);
    } catch (error) {
      console.error('Batch processing failed:', error);
    }
  }
}

/**
 * Monitor frame rate (for React Native)
 */
export class FrameRateMonitor {
  private frameCount = 0;
  private lastTime = Date.now();
  private fps = 0;
  private isMonitoring = false;

  start(): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.frameCount = 0;
    this.lastTime = Date.now();
    
    this.measureFrame();
  }

  stop(): void {
    this.isMonitoring = false;
  }

  getFPS(): number {
    return this.fps;
  }

  private measureFrame = (): void => {
    if (!this.isMonitoring) return;

    this.frameCount++;
    const currentTime = Date.now();
    const elapsed = currentTime - this.lastTime;

    if (elapsed >= 1000) {
      this.fps = Math.round((this.frameCount * 1000) / elapsed);
      this.frameCount = 0;
      this.lastTime = currentTime;
    }

    requestAnimationFrame(this.measureFrame);
  };
}

/**
 * Performance optimization tips
 */
export const PERFORMANCE_TIPS = {
  // Image optimization
  optimizeImages: {
    tip: 'Use appropriate image formats and sizes',
    implementation: 'Resize images before upload, use WebP format when possible',
  },
  
  // List optimization
  optimizeLists: {
    tip: 'Use FlatList for large datasets',
    implementation: 'Implement getItemLayout, keyExtractor, and removeClippedSubviews',
  },
  
  // Memory management
  memoryManagement: {
    tip: 'Clean up resources and listeners',
    implementation: 'Use useEffect cleanup, remove event listeners, clear timers',
  },
  
  // Network optimization
  networkOptimization: {
    tip: 'Batch network requests and implement caching',
    implementation: 'Use request queuing, implement response caching, compress data',
  },
  
  // State management
  stateOptimization: {
    tip: 'Minimize re-renders with proper state structure',
    implementation: 'Use React.memo, useMemo, useCallback appropriately',
  },
};

/**
 * Log performance tips
 */
export function logPerformanceTips(): void {
  console.log('=== Performance Tips ===');
  Object.entries(PERFORMANCE_TIPS).forEach(([key, tip]) => {
    console.log(`${key}:`);
    console.log(`  Tip: ${tip.tip}`);
    console.log(`  Implementation: ${tip.implementation}`);
  });
  console.log('=======================');
}
