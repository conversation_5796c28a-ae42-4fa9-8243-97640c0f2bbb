import { S3Object, SearchFilter, AppSettings } from '../types';

/**
 * Utility helper functions
 */

/**
 * Debounce function to limit the rate of function calls
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function to limit the rate of function calls
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Generate a unique ID
 */
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * Deep clone an object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
  
  return obj;
}

/**
 * Sort S3 objects based on settings
 */
export function sortObjects(objects: S3Object[], settings: AppSettings): S3Object[] {
  const { sortBy, sortOrder } = settings;
  
  return [...objects].sort((a, b) => {
    let comparison = 0;
    
    // Folders always come first
    if (a.isFolder && !b.isFolder) return -1;
    if (!a.isFolder && b.isFolder) return 1;
    
    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name, undefined, { numeric: true });
        break;
      case 'date':
        comparison = a.lastModified.getTime() - b.lastModified.getTime();
        break;
      case 'size':
        comparison = a.size - b.size;
        break;
      default:
        comparison = a.name.localeCompare(b.name);
    }
    
    return sortOrder === 'desc' ? -comparison : comparison;
  });
}

/**
 * Filter S3 objects based on search criteria
 */
export function filterObjects(objects: S3Object[], filter: SearchFilter): S3Object[] {
  return objects.filter(obj => {
    // Text search
    if (filter.query && !obj.name.toLowerCase().includes(filter.query.toLowerCase())) {
      return false;
    }
    
    // File type filter
    if (filter.fileType && filter.fileType !== 'all') {
      const fileExtension = obj.name.split('.').pop()?.toLowerCase() || '';
      
      switch (filter.fileType) {
        case 'image':
          if (!['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(fileExtension)) {
            return false;
          }
          break;
        case 'video':
          if (!['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(fileExtension)) {
            return false;
          }
          break;
        case 'audio':
          if (!['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'].includes(fileExtension)) {
            return false;
          }
          break;
        case 'document':
          if (!['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'].includes(fileExtension)) {
            return false;
          }
          break;
      }
    }
    
    // Date range filter
    if (filter.dateRange) {
      const objDate = obj.lastModified;
      if (objDate < filter.dateRange.start || objDate > filter.dateRange.end) {
        return false;
      }
    }
    
    // Size range filter
    if (filter.sizeRange && !obj.isFolder) {
      if (obj.size < filter.sizeRange.min || obj.size > filter.sizeRange.max) {
        return false;
      }
    }
    
    return true;
  });
}

/**
 * Group objects by folder structure
 */
export function groupObjectsByFolder(objects: S3Object[]): { [folder: string]: S3Object[] } {
  const grouped: { [folder: string]: S3Object[] } = {};
  
  objects.forEach(obj => {
    const folderPath = obj.path.split('/').slice(0, -1).join('/') || 'root';
    
    if (!grouped[folderPath]) {
      grouped[folderPath] = [];
    }
    
    grouped[folderPath].push(obj);
  });
  
  return grouped;
}

/**
 * Calculate total size of objects
 */
export function calculateTotalSize(objects: S3Object[]): number {
  return objects.reduce((total, obj) => total + (obj.isFolder ? 0 : obj.size), 0);
}

/**
 * Get breadcrumb items from path
 */
export function getBreadcrumbs(path: string): Array<{ name: string; path: string }> {
  if (!path || path === '/') {
    return [{ name: 'Root', path: '' }];
  }
  
  const parts = path.split('/').filter(Boolean);
  const breadcrumbs = [{ name: 'Root', path: '' }];
  
  let currentPath = '';
  parts.forEach(part => {
    currentPath += `/${part}`;
    breadcrumbs.push({
      name: part,
      path: currentPath.substring(1), // Remove leading slash
    });
  });
  
  return breadcrumbs;
}

/**
 * Validate AWS credentials format
 */
export function validateAWSCredentials(credentials: {
  accessKeyId: string;
  secretAccessKey: string;
  region: string;
  bucketName: string;
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Access Key ID validation
  if (!credentials.accessKeyId) {
    errors.push('Access Key ID is required');
  } else if (!/^[A-Z0-9]{20}$/.test(credentials.accessKeyId)) {
    errors.push('Access Key ID must be 20 characters long and contain only uppercase letters and numbers');
  }
  
  // Secret Access Key validation
  if (!credentials.secretAccessKey) {
    errors.push('Secret Access Key is required');
  } else if (credentials.secretAccessKey.length !== 40) {
    errors.push('Secret Access Key must be 40 characters long');
  }
  
  // Region validation
  if (!credentials.region) {
    errors.push('Region is required');
  }
  
  // Bucket name validation
  if (!credentials.bucketName) {
    errors.push('Bucket name is required');
  } else {
    const bucketNameRegex = /^[a-z0-9][a-z0-9.-]*[a-z0-9]$/;
    if (!bucketNameRegex.test(credentials.bucketName) || 
        credentials.bucketName.length < 3 || 
        credentials.bucketName.length > 63) {
      errors.push('Invalid bucket name format');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Format bytes to human readable string
 */
export function formatBytes(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Format duration in milliseconds to human readable string
 */
export function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * Check if a string is a valid URL
 */
export function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

/**
 * Truncate text to specified length
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength - 3) + '...';
}

/**
 * Get file icon name based on file type
 */
export function getFileIcon(filename: string, isFolder: boolean = false): string {
  if (isFolder) {
    return 'folder';
  }
  
  const extension = filename.split('.').pop()?.toLowerCase() || '';
  
  // Image files
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(extension)) {
    return 'image';
  }
  
  // Video files
  if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(extension)) {
    return 'video-camera';
  }
  
  // Audio files
  if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'].includes(extension)) {
    return 'music';
  }
  
  // Document files
  if (['pdf'].includes(extension)) {
    return 'file-pdf-o';
  }
  
  if (['doc', 'docx'].includes(extension)) {
    return 'file-word-o';
  }
  
  if (['xls', 'xlsx'].includes(extension)) {
    return 'file-excel-o';
  }
  
  if (['ppt', 'pptx'].includes(extension)) {
    return 'file-powerpoint-o';
  }
  
  if (['txt', 'rtf'].includes(extension)) {
    return 'file-text-o';
  }
  
  // Archive files
  if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
    return 'file-archive-o';
  }
  
  // Code files
  if (['js', 'ts', 'jsx', 'tsx', 'html', 'css', 'json', 'xml'].includes(extension)) {
    return 'file-code-o';
  }
  
  return 'file-o';
}
