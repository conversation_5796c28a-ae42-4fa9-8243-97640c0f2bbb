import { Alert } from 'react-native';
import { S3Error } from '../types';
import { maskSensitiveInfo } from './security';

/**
 * Error handling utilities for the S3 Visualizer app
 */

export interface AppError {
  code: string;
  message: string;
  details?: string;
  timestamp: Date;
  context?: Record<string, any>;
}

/**
 * Error codes for different types of errors
 */
export const ERROR_CODES = {
  // Network errors
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  CONNECTION_FAILED: 'CONNECTION_FAILED',
  
  // Authentication errors
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  ACCESS_DENIED: 'ACCESS_DENIED',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  
  // S3 errors
  BUCKET_NOT_FOUND: 'BUCKET_NOT_FOUND',
  OBJECT_NOT_FOUND: 'OBJECT_NOT_FOUND',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  STORAGE_QUOTA_EXCEEDED: 'STORAGE_QUOTA_EXCEEDED',
  
  // File operation errors
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  UPLOAD_FAILED: 'UPLOAD_FAILED',
  DOWNLOAD_FAILED: 'DOWNLOAD_FAILED',
  DELETE_FAILED: 'DELETE_FAILED',
  
  // Validation errors
  INVALID_INPUT: 'INVALID_INPUT',
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  
  // System errors
  STORAGE_ERROR: 'STORAGE_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

/**
 * User-friendly error messages
 */
const ERROR_MESSAGES: Record<string, string> = {
  [ERROR_CODES.NETWORK_ERROR]: 'Network connection error. Please check your internet connection and try again.',
  [ERROR_CODES.TIMEOUT_ERROR]: 'Request timed out. Please try again.',
  [ERROR_CODES.CONNECTION_FAILED]: 'Failed to connect to AWS S3. Please check your credentials and network connection.',
  
  [ERROR_CODES.INVALID_CREDENTIALS]: 'Invalid AWS credentials. Please check your Access Key ID and Secret Access Key.',
  [ERROR_CODES.ACCESS_DENIED]: 'Access denied. Please check your AWS permissions.',
  [ERROR_CODES.TOKEN_EXPIRED]: 'Your session has expired. Please log in again.',
  
  [ERROR_CODES.BUCKET_NOT_FOUND]: 'The specified S3 bucket was not found or you do not have access to it.',
  [ERROR_CODES.OBJECT_NOT_FOUND]: 'The requested file was not found.',
  [ERROR_CODES.INSUFFICIENT_PERMISSIONS]: 'You do not have sufficient permissions to perform this operation.',
  [ERROR_CODES.STORAGE_QUOTA_EXCEEDED]: 'Storage quota exceeded. Please free up space or upgrade your plan.',
  
  [ERROR_CODES.FILE_TOO_LARGE]: 'File is too large. Maximum file size is 5GB.',
  [ERROR_CODES.INVALID_FILE_TYPE]: 'File type not allowed. Please select a different file.',
  [ERROR_CODES.UPLOAD_FAILED]: 'File upload failed. Please try again.',
  [ERROR_CODES.DOWNLOAD_FAILED]: 'File download failed. Please try again.',
  [ERROR_CODES.DELETE_FAILED]: 'Failed to delete file. Please try again.',
  
  [ERROR_CODES.INVALID_INPUT]: 'Invalid input. Please check your data and try again.',
  [ERROR_CODES.VALIDATION_FAILED]: 'Validation failed. Please check your input.',
  
  [ERROR_CODES.STORAGE_ERROR]: 'Local storage error. Please restart the app.',
  [ERROR_CODES.UNKNOWN_ERROR]: 'An unexpected error occurred. Please try again.',
};

/**
 * Convert S3 error to app error
 */
export function mapS3Error(s3Error: S3Error): AppError {
  let code = ERROR_CODES.UNKNOWN_ERROR;
  let message = ERROR_MESSAGES[ERROR_CODES.UNKNOWN_ERROR];

  // Map AWS error codes to app error codes
  switch (s3Error.code) {
    case 'NetworkingError':
    case 'TimeoutError':
      code = ERROR_CODES.NETWORK_ERROR;
      break;
    case 'InvalidAccessKeyId':
    case 'SignatureDoesNotMatch':
      code = ERROR_CODES.INVALID_CREDENTIALS;
      break;
    case 'AccessDenied':
    case 'Forbidden':
      code = ERROR_CODES.ACCESS_DENIED;
      break;
    case 'NoSuchBucket':
      code = ERROR_CODES.BUCKET_NOT_FOUND;
      break;
    case 'NoSuchKey':
      code = ERROR_CODES.OBJECT_NOT_FOUND;
      break;
    case 'EntityTooLarge':
      code = ERROR_CODES.FILE_TOO_LARGE;
      break;
    default:
      // Try to infer from status code
      if (s3Error.statusCode) {
        if (s3Error.statusCode === 401 || s3Error.statusCode === 403) {
          code = ERROR_CODES.ACCESS_DENIED;
        } else if (s3Error.statusCode === 404) {
          code = ERROR_CODES.OBJECT_NOT_FOUND;
        } else if (s3Error.statusCode >= 500) {
          code = ERROR_CODES.NETWORK_ERROR;
        }
      }
  }

  message = ERROR_MESSAGES[code] || s3Error.message;

  return {
    code,
    message,
    details: s3Error.message,
    timestamp: new Date(),
    context: {
      originalCode: s3Error.code,
      statusCode: s3Error.statusCode,
      requestId: s3Error.requestId,
    },
  };
}

/**
 * Create an app error
 */
export function createAppError(
  code: string,
  message?: string,
  details?: string,
  context?: Record<string, any>
): AppError {
  return {
    code,
    message: message || ERROR_MESSAGES[code] || ERROR_MESSAGES[ERROR_CODES.UNKNOWN_ERROR],
    details,
    timestamp: new Date(),
    context,
  };
}

/**
 * Log error for debugging (with sensitive info masked)
 */
export function logError(error: AppError | Error, context?: Record<string, any>): void {
  const errorInfo = {
    message: error.message,
    stack: error instanceof Error ? error.stack : undefined,
    context: context || ('context' in error ? error.context : undefined),
    timestamp: 'timestamp' in error ? error.timestamp : new Date(),
  };

  // Mask sensitive information
  const maskedInfo = maskSensitiveInfo(JSON.stringify(errorInfo, null, 2));
  
  console.error('App Error:', maskedInfo);
  
  // In production, you might want to send this to a logging service
  // logToService(maskedInfo);
}

/**
 * Show user-friendly error alert
 */
export function showErrorAlert(
  error: AppError | Error | string,
  title: string = 'Error',
  showDetails: boolean = false
): void {
  let message: string;
  let details: string | undefined;

  if (typeof error === 'string') {
    message = error;
  } else if (error instanceof Error) {
    message = error.message;
    details = error.stack;
  } else {
    message = error.message;
    details = error.details;
  }

  const alertMessage = showDetails && details 
    ? `${message}\n\nDetails: ${details}`
    : message;

  Alert.alert(title, alertMessage, [{ text: 'OK' }]);
}

/**
 * Handle async errors with user feedback
 */
export async function handleAsyncError<T>(
  operation: () => Promise<T>,
  errorTitle: string = 'Operation Failed',
  showAlert: boolean = true
): Promise<T | null> {
  try {
    return await operation();
  } catch (error) {
    const appError = error instanceof Error 
      ? createAppError(ERROR_CODES.UNKNOWN_ERROR, error.message)
      : createAppError(ERROR_CODES.UNKNOWN_ERROR, 'Unknown error occurred');

    logError(appError);

    if (showAlert) {
      showErrorAlert(appError, errorTitle);
    }

    return null;
  }
}

/**
 * Retry mechanism for failed operations
 */
export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
  backoffMultiplier: number = 2
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      // Wait before retrying with exponential backoff
      const waitTime = delay * Math.pow(backoffMultiplier, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
  
  throw lastError!;
}

/**
 * Circuit breaker pattern for preventing cascading failures
 */
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(
    private maxFailures: number = 5,
    private timeout: number = 60000 // 1 minute
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime < this.timeout) {
        throw new Error('Circuit breaker is OPEN');
      } else {
        this.state = 'HALF_OPEN';
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.maxFailures) {
      this.state = 'OPEN';
    }
  }

  reset(): void {
    this.failures = 0;
    this.state = 'CLOSED';
    this.lastFailureTime = 0;
  }
}

export const circuitBreaker = new CircuitBreaker();

/**
 * Global error boundary for unhandled errors
 */
export function setupGlobalErrorHandler(): void {
  // Handle unhandled promise rejections
  if (typeof global !== 'undefined' && global.process) {
    global.process.on?.('unhandledRejection', (reason: any) => {
      const error = createAppError(
        ERROR_CODES.UNKNOWN_ERROR,
        'Unhandled promise rejection',
        reason?.toString(),
        { reason }
      );
      logError(error);
    });
  }

  // Handle uncaught exceptions
  if (typeof global !== 'undefined' && global.process) {
    global.process.on?.('uncaughtException', (error: Error) => {
      const appError = createAppError(
        ERROR_CODES.UNKNOWN_ERROR,
        'Uncaught exception',
        error.message,
        { stack: error.stack }
      );
      logError(appError);
    });
  }
}
