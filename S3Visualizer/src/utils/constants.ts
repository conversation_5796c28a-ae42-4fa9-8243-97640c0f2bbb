// Application constants

export const APP_CONFIG = {
  NAME: 'S3 Visualizer',
  VERSION: '1.0.0',
  DESCRIPTION: 'Google Drive-like interface for AWS S3 storage',
};

export const AWS_REGIONS = [
  { label: 'US East (N. Virginia)', value: 'us-east-1' },
  { label: 'US East (Ohio)', value: 'us-east-2' },
  { label: 'US West (N. California)', value: 'us-west-1' },
  { label: 'US West (Oregon)', value: 'us-west-2' },
  { label: 'Africa (Cape Town)', value: 'af-south-1' },
  { label: 'Asia Pacific (Hong Kong)', value: 'ap-east-1' },
  { label: 'Asia Pacific (Mumbai)', value: 'ap-south-1' },
  { label: 'Asia Pacific (Osaka)', value: 'ap-northeast-3' },
  { label: 'Asia Pacific (Seoul)', value: 'ap-northeast-2' },
  { label: 'Asia Pacific (Singapore)', value: 'ap-southeast-1' },
  { label: 'Asia Pacific (Sydney)', value: 'ap-southeast-2' },
  { label: 'Asia Pacific (Tokyo)', value: 'ap-northeast-1' },
  { label: 'Canada (Central)', value: 'ca-central-1' },
  { label: 'Europe (Frankfurt)', value: 'eu-central-1' },
  { label: 'Europe (Ireland)', value: 'eu-west-1' },
  { label: 'Europe (London)', value: 'eu-west-2' },
  { label: 'Europe (Milan)', value: 'eu-south-1' },
  { label: 'Europe (Paris)', value: 'eu-west-3' },
  { label: 'Europe (Stockholm)', value: 'eu-north-1' },
  { label: 'Middle East (Bahrain)', value: 'me-south-1' },
  { label: 'South America (São Paulo)', value: 'sa-east-1' },
];

export const FILE_TYPES = {
  IMAGE: 'image',
  VIDEO: 'video',
  AUDIO: 'audio',
  DOCUMENT: 'document',
  ARCHIVE: 'archive',
  OTHER: 'other',
} as const;

export const SORT_OPTIONS = [
  { label: 'Name', value: 'name' },
  { label: 'Date Modified', value: 'date' },
  { label: 'Size', value: 'size' },
] as const;

export const VIEW_MODES = {
  LIST: 'list',
  GRID: 'grid',
} as const;

export const UPLOAD_LIMITS = {
  MAX_FILE_SIZE: 5 * 1024 * 1024 * 1024, // 5GB
  MAX_CONCURRENT_UPLOADS: 5,
  CHUNK_SIZE: 5 * 1024 * 1024, // 5MB for multipart uploads
  MULTIPART_THRESHOLD: 100 * 1024 * 1024, // 100MB
} as const;

export const REFRESH_INTERVALS = [
  { label: '15 seconds', value: 15 },
  { label: '30 seconds', value: 30 },
  { label: '1 minute', value: 60 },
  { label: '5 minutes', value: 300 },
  { label: '10 minutes', value: 600 },
] as const;

export const COLORS = {
  PRIMARY: '#007AFF',
  SECONDARY: '#5856D6',
  SUCCESS: '#34C759',
  WARNING: '#FF9500',
  ERROR: '#FF3B30',
  INFO: '#5AC8FA',
  LIGHT: '#F2F2F7',
  DARK: '#1C1C1E',
  GRAY: '#8E8E93',
  BACKGROUND: '#FFFFFF',
  SURFACE: '#F2F2F7',
  TEXT_PRIMARY: '#000000',
  TEXT_SECONDARY: '#8E8E93',
  BORDER: '#C6C6C8',
} as const;

export const SPACING = {
  XS: 4,
  SM: 8,
  MD: 16,
  LG: 24,
  XL: 32,
  XXL: 48,
} as const;

export const FONT_SIZES = {
  XS: 12,
  SM: 14,
  MD: 16,
  LG: 18,
  XL: 20,
  XXL: 24,
  XXXL: 32,
} as const;

export const ICON_SIZES = {
  XS: 16,
  SM: 20,
  MD: 24,
  LG: 32,
  XL: 40,
} as const;

export const ANIMATION_DURATION = {
  FAST: 200,
  NORMAL: 300,
  SLOW: 500,
} as const;

export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection error. Please check your internet connection.',
  INVALID_CREDENTIALS: 'Invalid AWS credentials. Please check your access key and secret key.',
  BUCKET_NOT_FOUND: 'The specified bucket does not exist or you do not have access to it.',
  PERMISSION_DENIED: 'You do not have permission to perform this operation.',
  FILE_TOO_LARGE: 'File is too large. Maximum file size is 5GB.',
  UPLOAD_FAILED: 'File upload failed. Please try again.',
  DOWNLOAD_FAILED: 'File download failed. Please try again.',
  DELETE_FAILED: 'Failed to delete file. Please try again.',
  GENERIC_ERROR: 'An unexpected error occurred. Please try again.',
} as const;

export const SUCCESS_MESSAGES = {
  CREDENTIALS_SAVED: 'AWS credentials saved successfully.',
  FILE_UPLOADED: 'File uploaded successfully.',
  FILE_DOWNLOADED: 'File downloaded successfully.',
  FILE_DELETED: 'File deleted successfully.',
  FOLDER_CREATED: 'Folder created successfully.',
  SETTINGS_SAVED: 'Settings saved successfully.',
} as const;

export const PLACEHOLDER_TEXTS = {
  SEARCH: 'Search files and folders...',
  ACCESS_KEY: 'Enter your AWS Access Key ID',
  SECRET_KEY: 'Enter your AWS Secret Access Key',
  BUCKET_NAME: 'Enter your S3 bucket name',
  FOLDER_NAME: 'Enter folder name',
  NO_FILES: 'No files found in this folder',
  NO_SEARCH_RESULTS: 'No files match your search criteria',
} as const;
