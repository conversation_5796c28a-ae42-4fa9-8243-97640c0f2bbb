import { Alert } from 'react-native';

/**
 * Security utilities for the S3 Visualizer app
 */

/**
 * Validate and sanitize file names to prevent security issues
 */
export function sanitizeFileName(fileName: string): string {
  // Remove or replace dangerous characters
  return fileName
    .replace(/[<>:"|?*\x00-\x1f]/g, '_') // Replace invalid characters
    .replace(/^\.+/, '') // Remove leading dots
    .replace(/\.+$/, '') // Remove trailing dots
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .substring(0, 255); // Limit length
}

/**
 * Validate S3 key to prevent path traversal attacks
 */
export function validateS3Key(key: string): boolean {
  // Check for path traversal attempts
  if (key.includes('../') || key.includes('..\\')) {
    return false;
  }
  
  // Check for absolute paths
  if (key.startsWith('/') || key.includes(':\\')) {
    return false;
  }
  
  // Check for null bytes
  if (key.includes('\0')) {
    return false;
  }
  
  // Check length
  if (key.length > 1024) {
    return false;
  }
  
  return true;
}

/**
 * Validate AWS credentials format
 */
export function validateAWSCredentialsFormat(credentials: {
  accessKeyId: string;
  secretAccessKey: string;
  region: string;
  bucketName: string;
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Access Key ID validation (20 characters, alphanumeric)
  if (!credentials.accessKeyId) {
    errors.push('Access Key ID is required');
  } else if (!/^[A-Z0-9]{20}$/.test(credentials.accessKeyId)) {
    errors.push('Invalid Access Key ID format');
  }
  
  // Secret Access Key validation (40 characters)
  if (!credentials.secretAccessKey) {
    errors.push('Secret Access Key is required');
  } else if (credentials.secretAccessKey.length !== 40) {
    errors.push('Invalid Secret Access Key format');
  }
  
  // Region validation
  if (!credentials.region) {
    errors.push('Region is required');
  } else if (!/^[a-z0-9-]+$/.test(credentials.region)) {
    errors.push('Invalid region format');
  }
  
  // Bucket name validation
  if (!credentials.bucketName) {
    errors.push('Bucket name is required');
  } else {
    const bucketErrors = validateBucketName(credentials.bucketName);
    errors.push(...bucketErrors);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Validate S3 bucket name according to AWS rules
 */
export function validateBucketName(bucketName: string): string[] {
  const errors: string[] = [];
  
  // Length check
  if (bucketName.length < 3 || bucketName.length > 63) {
    errors.push('Bucket name must be between 3 and 63 characters');
  }
  
  // Character check
  if (!/^[a-z0-9.-]+$/.test(bucketName)) {
    errors.push('Bucket name can only contain lowercase letters, numbers, dots, and hyphens');
  }
  
  // Start and end check
  if (!/^[a-z0-9]/.test(bucketName) || !/[a-z0-9]$/.test(bucketName)) {
    errors.push('Bucket name must start and end with a letter or number');
  }
  
  // Consecutive dots check
  if (bucketName.includes('..')) {
    errors.push('Bucket name cannot contain consecutive dots');
  }
  
  // IP address check
  if (/^\d+\.\d+\.\d+\.\d+$/.test(bucketName)) {
    errors.push('Bucket name cannot be formatted as an IP address');
  }
  
  // Reserved prefixes
  if (bucketName.startsWith('xn--') || bucketName.startsWith('sthree-')) {
    errors.push('Bucket name uses a reserved prefix');
  }
  
  // Reserved suffixes
  if (bucketName.endsWith('-s3alias') || bucketName.endsWith('--ol-s3')) {
    errors.push('Bucket name uses a reserved suffix');
  }
  
  return errors;
}

/**
 * Check if a file type is allowed for upload
 */
export function isFileTypeAllowed(fileName: string, allowedTypes?: string[]): boolean {
  if (!allowedTypes || allowedTypes.length === 0) {
    return true; // Allow all types if no restrictions
  }
  
  const extension = fileName.split('.').pop()?.toLowerCase() || '';
  return allowedTypes.includes(extension);
}

/**
 * Check if file size is within limits
 */
export function isFileSizeAllowed(size: number, maxSize: number = 5 * 1024 * 1024 * 1024): boolean {
  return size <= maxSize;
}

/**
 * Mask sensitive information for logging
 */
export function maskSensitiveInfo(text: string): string {
  // Mask AWS Access Key ID (show first 4 and last 4 characters)
  text = text.replace(/\b[A-Z0-9]{20}\b/g, (match) => {
    return match.substring(0, 4) + '****' + match.substring(16);
  });
  
  // Mask AWS Secret Access Key (show first 4 characters only)
  text = text.replace(/\b[A-Za-z0-9+/]{40}\b/g, (match) => {
    return match.substring(0, 4) + '****';
  });
  
  return text;
}

/**
 * Generate a secure random string
 */
export function generateSecureId(length: number = 16): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Rate limiting utility
 */
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  
  isAllowed(key: string, maxRequests: number, windowMs: number): boolean {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Get existing requests for this key
    const requests = this.requests.get(key) || [];
    
    // Filter out old requests
    const recentRequests = requests.filter(time => time > windowStart);
    
    // Check if we're within the limit
    if (recentRequests.length >= maxRequests) {
      return false;
    }
    
    // Add current request
    recentRequests.push(now);
    this.requests.set(key, recentRequests);
    
    return true;
  }
  
  reset(key?: string): void {
    if (key) {
      this.requests.delete(key);
    } else {
      this.requests.clear();
    }
  }
}

export const rateLimiter = new RateLimiter();

/**
 * Security headers for HTTP requests
 */
export const getSecurityHeaders = () => ({
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
});

/**
 * Validate URL to prevent SSRF attacks
 */
export function isValidUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    
    // Only allow HTTPS for external URLs
    if (!['https:', 'http:'].includes(parsedUrl.protocol)) {
      return false;
    }
    
    // Block private IP ranges
    const hostname = parsedUrl.hostname;
    if (
      hostname === 'localhost' ||
      hostname === '127.0.0.1' ||
      hostname.startsWith('192.168.') ||
      hostname.startsWith('10.') ||
      hostname.startsWith('172.')
    ) {
      return false;
    }
    
    return true;
  } catch {
    return false;
  }
}

/**
 * Show security warning for sensitive operations
 */
export function showSecurityWarning(
  title: string,
  message: string,
  onConfirm: () => void,
  onCancel?: () => void
): void {
  Alert.alert(
    title,
    message,
    [
      {
        text: 'Cancel',
        style: 'cancel',
        onPress: onCancel,
      },
      {
        text: 'Continue',
        style: 'destructive',
        onPress: onConfirm,
      },
    ],
    { cancelable: false }
  );
}
