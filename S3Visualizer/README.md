# S3 Visualizer

A Google Drive-like interface for AWS S3 storage built with React Native and TypeScript.

![S3 Visualizer](https://img.shields.io/badge/React%20Native-0.72-blue.svg)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)
![AWS SDK](https://img.shields.io/badge/AWS%20SDK-v3-orange.svg)
![License](https://img.shields.io/badge/License-MIT-green.svg)

## 🚀 Features

### Core Features
- **Secure AWS S3 Integration** - Connect to your S3 buckets with AWS credentials
- **Google Drive-like Interface** - Intuitive file and folder management
- **File Operations** - Upload, download, delete, copy, move, and rename files
- **Folder Management** - Create, navigate, and organize folders
- **Search & Filter** - Find files quickly with advanced search options
- **Progress Tracking** - Real-time upload/download progress monitoring

### Advanced Features
- **Multipart Uploads** - Efficient handling of large files (>100MB)
- **Batch Operations** - Select and operate on multiple files
- **Shareable Links** - Generate presigned URLs for file sharing
- **Offline Support** - Cached file listings and offline-first design
- **Security** - Biometric authentication and secure credential storage
- **Performance** - Optimized for large file collections

### UI/UX Features
- **Dual View Modes** - List and grid view options
- **Dark/Light Theme** - Adaptive theme support
- **Responsive Design** - Works on phones and tablets
- **Gesture Support** - Swipe actions and long-press menus
- **Accessibility** - Full accessibility support

## 📱 Screenshots

| Setup Screen | Home Dashboard | File Browser | Settings |
|--------------|----------------|--------------|----------|
| ![Setup](docs/screenshots/setup.png) | ![Home](docs/screenshots/home.png) | ![Files](docs/screenshots/files.png) | ![Settings](docs/screenshots/settings.png) |

## 🛠 Installation

### Prerequisites
- Node.js 18+ and npm/yarn
- React Native development environment
- iOS Simulator (for iOS) or Android Emulator (for Android)
- AWS Account with S3 access

### Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/s3-visualizer.git
   cd s3-visualizer/S3Visualizer
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Install iOS dependencies** (iOS only)
   ```bash
   cd ios && pod install && cd ..
   ```

4. **Start the development server**
   ```bash
   npm start
   # or
   yarn start
   ```

5. **Run on device/simulator**
   ```bash
   # iOS
   npm run ios
   # or
   yarn ios
   
   # Android
   npm run android
   # or
   yarn android
   ```

## ⚙️ Configuration

### AWS Setup

1. **Create AWS IAM User**
   - Go to AWS IAM Console
   - Create a new user with programmatic access
   - Attach the following policy (or create a custom one):

   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": [
           "s3:ListBucket",
           "s3:GetObject",
           "s3:PutObject",
           "s3:DeleteObject",
           "s3:GetObjectAcl",
           "s3:PutObjectAcl"
         ],
         "Resource": [
           "arn:aws:s3:::your-bucket-name",
           "arn:aws:s3:::your-bucket-name/*"
         ]
       }
     ]
   }
   ```

2. **Create S3 Bucket**
   - Create a new S3 bucket in your preferred region
   - Configure CORS if needed for web access
   - Set appropriate bucket policies

3. **Get Credentials**
   - Note down the Access Key ID and Secret Access Key
   - You'll need these to configure the app

### App Configuration

The app will prompt you to enter your AWS credentials on first launch:

- **Access Key ID**: Your AWS access key
- **Secret Access Key**: Your AWS secret key
- **Region**: Your S3 bucket region (e.g., us-east-1)
- **Bucket Name**: Your S3 bucket name

## 📖 Usage Guide

### First Time Setup
1. Launch the app
2. Enter your AWS credentials
3. Test the connection
4. Start browsing your S3 files!

### File Operations
- **Upload**: Tap the upload button and select files
- **Download**: Tap a file and select "Download"
- **Delete**: Long press a file and select "Delete"
- **Share**: Generate shareable links for files
- **Organize**: Create folders and move files

### Advanced Features
- **Batch Operations**: Long press to enter selection mode
- **Search**: Use the search bar to find files quickly
- **Settings**: Customize view modes, sorting, and app behavior

## 🧪 Testing

### Running Tests
```bash
# Run all tests
npm test
# or
yarn test

# Run tests with coverage
npm run test:coverage
# or
yarn test:coverage

# Run specific test file
npm test -- utils.test.ts
```

### Manual Testing
See [TESTING.md](docs/TESTING.md) for comprehensive testing guidelines.

## 🏗 Architecture

### Project Structure
```
S3Visualizer/
├── src/
│   ├── components/     # Reusable UI components
│   ├── screens/        # Main app screens
│   ├── services/       # Business logic and API calls
│   ├── utils/          # Helper functions and utilities
│   ├── types/          # TypeScript type definitions
│   └── context/        # React Context for state management
├── docs/               # Documentation
├── __tests__/          # Test files
└── ...
```

### Key Technologies
- **React Native** - Cross-platform mobile framework
- **TypeScript** - Type-safe JavaScript
- **AWS SDK v3** - AWS service integration
- **React Navigation** - Navigation library
- **React Native Keychain** - Secure storage
- **React Native Vector Icons** - Icon library

## 🔒 Security

### Data Protection
- **Secure Storage**: AWS credentials stored using device keychain
- **Biometric Auth**: Optional biometric authentication
- **Input Validation**: All user inputs are validated and sanitized
- **Network Security**: HTTPS-only communication with AWS

### Best Practices
- Never store credentials in plain text
- Use least-privilege IAM policies
- Regularly rotate access keys
- Monitor AWS CloudTrail logs

## 🚀 Deployment

### Building for Production

#### iOS
```bash
# Build for iOS
npx react-native run-ios --configuration Release
```

#### Android
```bash
# Build APK
cd android && ./gradlew assembleRelease

# Build AAB (for Play Store)
cd android && ./gradlew bundleRelease
```

### App Store Submission
See [DEPLOYMENT.md](docs/DEPLOYMENT.md) for detailed deployment instructions.

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](docs/CONTRIBUTING.md) for guidelines.

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Documentation
- [Setup Guide](docs/SETUP.md)
- [User Guide](docs/USER_GUIDE.md)
- [API Reference](docs/API.md)
- [Troubleshooting](docs/TROUBLESHOOTING.md)

### Getting Help
- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/s3visualizer)
- 🐛 Issues: [GitHub Issues](https://github.com/yourusername/s3-visualizer/issues)

## 🙏 Acknowledgments

- AWS SDK team for excellent documentation
- React Native community for amazing tools
- All contributors who helped make this project better

---

Made with ❤️ by the S3 Visualizer team
