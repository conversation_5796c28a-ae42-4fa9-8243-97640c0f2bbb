/**
 * Tests for utility functions
 */

import {
  formatBytes,
  validateAWSCredentials,
  sortObjects,
  filterObjects,
  getBreadcrumbs,
} from '../src/utils/helpers';
import {
  sanitizeFileName,
  validateS3Key,
  validateBucketName,
  isFileTypeAllowed,
  isFileSizeAllowed,
} from '../src/utils/security';
import { S3Object, AppSettings } from '../src/types';

// Mock data
const mockObjects: S3Object[] = [
  {
    key: 'folder1/',
    name: 'folder1',
    size: 0,
    lastModified: new Date('2023-01-01'),
    isFolder: true,
    path: 'folder1/',
  },
  {
    key: 'file1.txt',
    name: 'file1.txt',
    size: 1024,
    lastModified: new Date('2023-01-02'),
    isFolder: false,
    path: 'file1.txt',
  },
  {
    key: 'file2.jpg',
    name: 'file2.jpg',
    size: 2048,
    lastModified: new Date('2023-01-03'),
    isFolder: false,
    path: 'file2.jpg',
  },
];

const mockSettings: AppSettings = {
  viewMode: 'list',
  sortBy: 'name',
  sortOrder: 'asc',
  showHiddenFiles: false,
  autoRefresh: false,
  refreshInterval: 30,
  maxConcurrentUploads: 3,
  chunkSize: 5 * 1024 * 1024,
};

describe('Helper Functions', () => {
  describe('formatBytes', () => {
    test('formats bytes correctly', () => {
      expect(formatBytes(0)).toBe('0 Bytes');
      expect(formatBytes(1024)).toBe('1 KB');
      expect(formatBytes(1048576)).toBe('1 MB');
      expect(formatBytes(1073741824)).toBe('1 GB');
    });
  });

  describe('validateAWSCredentials', () => {
    test('validates correct credentials', () => {
      const credentials = {
        accessKeyId: 'AKIAIOSFODNN7EXAMPLE',
        secretAccessKey: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
        region: 'us-east-1',
        bucketName: 'my-test-bucket',
      };
      
      const result = validateAWSCredentials(credentials);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('rejects invalid credentials', () => {
      const credentials = {
        accessKeyId: 'invalid',
        secretAccessKey: 'invalid',
        region: '',
        bucketName: 'Invalid_Bucket_Name',
      };
      
      const result = validateAWSCredentials(credentials);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('sortObjects', () => {
    test('sorts objects by name ascending', () => {
      const settings = { ...mockSettings, sortBy: 'name' as const, sortOrder: 'asc' as const };
      const sorted = sortObjects(mockObjects, settings);
      
      // Folders should come first, then files sorted by name
      expect(sorted[0].isFolder).toBe(true);
      expect(sorted[1].name).toBe('file1.txt');
      expect(sorted[2].name).toBe('file2.jpg');
    });

    test('sorts objects by size descending', () => {
      const settings = { ...mockSettings, sortBy: 'size' as const, sortOrder: 'desc' as const };
      const sorted = sortObjects(mockObjects, settings);
      
      // Folders should come first, then files sorted by size descending
      expect(sorted[0].isFolder).toBe(true);
      expect(sorted[1].size).toBe(2048);
      expect(sorted[2].size).toBe(1024);
    });
  });

  describe('filterObjects', () => {
    test('filters objects by query', () => {
      const filtered = filterObjects(mockObjects, { query: 'file1' });
      expect(filtered).toHaveLength(1);
      expect(filtered[0].name).toBe('file1.txt');
    });

    test('filters objects by file type', () => {
      const filtered = filterObjects(mockObjects, { query: '', fileType: 'image' });
      expect(filtered).toHaveLength(1);
      expect(filtered[0].name).toBe('file2.jpg');
    });
  });

  describe('getBreadcrumbs', () => {
    test('returns root for empty path', () => {
      const breadcrumbs = getBreadcrumbs('');
      expect(breadcrumbs).toHaveLength(1);
      expect(breadcrumbs[0].name).toBe('Root');
    });

    test('returns correct breadcrumbs for nested path', () => {
      const breadcrumbs = getBreadcrumbs('folder1/subfolder/file.txt');
      expect(breadcrumbs).toHaveLength(4);
      expect(breadcrumbs[0].name).toBe('Root');
      expect(breadcrumbs[1].name).toBe('folder1');
      expect(breadcrumbs[2].name).toBe('subfolder');
      expect(breadcrumbs[3].name).toBe('file.txt');
    });
  });
});

describe('Security Functions', () => {
  describe('sanitizeFileName', () => {
    test('removes dangerous characters', () => {
      expect(sanitizeFileName('file<>:"|?*.txt')).toBe('file_______.txt');
      expect(sanitizeFileName('  file  name  ')).toBe('__file__name__');
    });

    test('removes leading and trailing dots', () => {
      expect(sanitizeFileName('...file.txt...')).toBe('file.txt');
    });
  });

  describe('validateS3Key', () => {
    test('accepts valid S3 keys', () => {
      expect(validateS3Key('folder/file.txt')).toBe(true);
      expect(validateS3Key('file-name_123.jpg')).toBe(true);
    });

    test('rejects invalid S3 keys', () => {
      expect(validateS3Key('../../../etc/passwd')).toBe(false);
      expect(validateS3Key('/absolute/path')).toBe(false);
      expect(validateS3Key('file\0name')).toBe(false);
    });
  });

  describe('validateBucketName', () => {
    test('accepts valid bucket names', () => {
      expect(validateBucketName('my-bucket-name')).toHaveLength(0);
      expect(validateBucketName('bucket123')).toHaveLength(0);
    });

    test('rejects invalid bucket names', () => {
      expect(validateBucketName('My-Bucket')).toContain('lowercase');
      expect(validateBucketName('ab')).toContain('between 3 and 63');
      expect(validateBucketName('bucket..name')).toContain('consecutive dots');
      expect(validateBucketName('***********')).toContain('IP address');
    });
  });

  describe('isFileTypeAllowed', () => {
    test('allows all types when no restrictions', () => {
      expect(isFileTypeAllowed('file.txt')).toBe(true);
      expect(isFileTypeAllowed('file.exe')).toBe(true);
    });

    test('respects allowed types list', () => {
      const allowedTypes = ['jpg', 'png', 'gif'];
      expect(isFileTypeAllowed('image.jpg', allowedTypes)).toBe(true);
      expect(isFileTypeAllowed('image.bmp', allowedTypes)).toBe(false);
    });
  });

  describe('isFileSizeAllowed', () => {
    test('allows files within size limit', () => {
      expect(isFileSizeAllowed(1024, 2048)).toBe(true);
      expect(isFileSizeAllowed(2048, 2048)).toBe(true);
    });

    test('rejects files exceeding size limit', () => {
      expect(isFileSizeAllowed(3072, 2048)).toBe(false);
    });
  });
});

// Mock tests for async functions (would need proper mocking in real tests)
describe('Integration Tests', () => {
  test('should handle file operations workflow', () => {
    // This would test the complete workflow:
    // 1. Validate credentials
    // 2. Connect to S3
    // 3. List objects
    // 4. Upload file
    // 5. Download file
    // 6. Delete file
    
    // For now, just test that the workflow components exist
    expect(validateAWSCredentials).toBeDefined();
    expect(sanitizeFileName).toBeDefined();
    expect(validateS3Key).toBeDefined();
  });
});
