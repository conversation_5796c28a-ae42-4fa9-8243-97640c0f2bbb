import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { View, StyleSheet, Alert } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import * as DocumentPicker from 'expo-document-picker';

import { AppProvider, useApp } from './src/context/AppContext';
import { SetupScreen, HomeScreen, FileListScreen, SettingsScreen } from './src/screens';
import { UploadModal, ProgressModal, ActionSheet } from './src/components';
import { S3Service } from './src/services';
import { S3Object, FileOperation, UploadProgress } from './src/types';
import { COLORS } from './src/utils/constants';

const Stack = createStackNavigator();

const AppContent: React.FC = () => {
  const { state, login, logout, updateSettings, addFileOperation, updateFileOperation } = useApp();
  const [currentScreen, setCurrentScreen] = useState<'setup' | 'home' | 'files' | 'settings'>('setup');
  const [currentPath, setCurrentPath] = useState('');
  const [currentFolderName, setCurrentFolderName] = useState('Root');
  const [selectedFile, setSelectedFile] = useState<S3Object | null>(null);

  // Modal states
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showProgressModal, setShowProgressModal] = useState(false);
  const [showActionSheet, setShowActionSheet] = useState(false);

  useEffect(() => {
    if (state.isAuthenticated) {
      setCurrentScreen('home');
    } else {
      setCurrentScreen('setup');
    }
  }, [state.isAuthenticated]);

  const handleSetupComplete = () => {
    setCurrentScreen('home');
  };

  const handleLogout = () => {
    logout();
    setCurrentScreen('setup');
  };

  const handleNavigateToFiles = (path: string = '', folderName: string = 'Root') => {
    setCurrentPath(path);
    setCurrentFolderName(folderName);
    setCurrentScreen('files');
  };

  const handleNavigateToSettings = () => {
    setCurrentScreen('settings');
  };

  const handleNavigateBack = () => {
    if (currentScreen === 'files') {
      setCurrentScreen('home');
    } else {
      setCurrentScreen('home');
    }
  };

  const handleFileSelect = (file: S3Object) => {
    setSelectedFile(file);
    setShowActionSheet(true);
  };

  const handleFilesSelected = async (
    files: DocumentPicker.DocumentPickerAsset[],
    path: string,
    options?: { storageClass?: string; serverSideEncryption?: string }
  ) => {
    if (!state.credentials) return;

    const s3Service = new S3Service();
    s3Service.initialize(state.credentials);

    for (const file of files) {
      const operationId = addFileOperation({
        type: 'upload',
        key: `${path}${path ? '/' : ''}${file.name}`,
        status: 'pending',
      });

      try {
        updateFileOperation(operationId, { status: 'in-progress' });

        const result = await s3Service.uploadFile(
          {
            uri: file.uri,
            name: file.name || 'unknown',
            type: file.type,
          },
          `${path}${path ? '/' : ''}${file.name}`,
          (progress: UploadProgress) => {
            updateFileOperation(operationId, { progress });
          },
          options
        );

        if (result.success) {
          updateFileOperation(operationId, {
            status: 'completed',
            endTime: new Date(),
          });
        } else {
          updateFileOperation(operationId, {
            status: 'failed',
            error: result.error?.message || 'Upload failed',
            endTime: new Date(),
          });
        }
      } catch (error) {
        updateFileOperation(operationId, {
          status: 'failed',
          error: 'Upload failed',
          endTime: new Date(),
        });
      }
    }
  };

  const handleDownload = async (item: S3Object) => {
    if (!state.credentials) return;

    const s3Service = new S3Service();
    s3Service.initialize(state.credentials);

    try {
      const result = await s3Service.downloadFile(item.key);
      if (result.success && result.data) {
        Alert.alert('Download Ready', 'File download URL generated successfully.');
        // In a real app, you would open the URL or save the file
      } else {
        Alert.alert('Error', 'Failed to generate download URL.');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to download file.');
    }
  };

  const handleShare = async (item: S3Object) => {
    if (!state.credentials) return;

    const s3Service = new S3Service();
    s3Service.initialize(state.credentials);

    try {
      const result = await s3Service.getShareableUrl(item.key, 3600); // 1 hour expiry
      if (result.success && result.data) {
        Alert.alert('Share Link', `Shareable URL: ${result.data}`);
        // In a real app, you would use the Share API
      } else {
        Alert.alert('Error', 'Failed to generate share link.');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to generate share link.');
    }
  };

  const handleDelete = async (item: S3Object) => {
    if (!state.credentials) return;

    Alert.alert(
      'Delete File',
      `Are you sure you want to delete "${item.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const s3Service = new S3Service();
            s3Service.initialize(state.credentials!);

            const operationId = addFileOperation({
              type: 'delete',
              key: item.key,
              status: 'in-progress',
            });

            try {
              const result = await s3Service.deleteObject(item.key);
              if (result.success) {
                updateFileOperation(operationId, {
                  status: 'completed',
                  endTime: new Date(),
                });
                Alert.alert('Success', 'File deleted successfully.');
              } else {
                updateFileOperation(operationId, {
                  status: 'failed',
                  error: result.error?.message || 'Delete failed',
                  endTime: new Date(),
                });
                Alert.alert('Error', 'Failed to delete file.');
              }
            } catch (error) {
              updateFileOperation(operationId, {
                status: 'failed',
                error: 'Delete failed',
                endTime: new Date(),
              });
              Alert.alert('Error', 'Failed to delete file.');
            }
          },
        },
      ]
    );
  };

  const renderCurrentScreen = () => {
    switch (currentScreen) {
      case 'setup':
        return <SetupScreen onSetupComplete={handleSetupComplete} />;

      case 'home':
        return (
          <HomeScreen
            onNavigateToFiles={() => handleNavigateToFiles()}
            onNavigateToSettings={handleNavigateToSettings}
            onLogout={handleLogout}
          />
        );

      case 'files':
        return (
          <FileListScreen
            path={currentPath}
            folderName={currentFolderName}
            onNavigateBack={handleNavigateBack}
            onNavigateToFolder={handleNavigateToFiles}
            onFileSelect={handleFileSelect}
          />
        );

      case 'settings':
        return (
          <SettingsScreen
            onNavigateBack={handleNavigateBack}
            onCredentialsUpdate={handleLogout}
          />
        );

      default:
        return <SetupScreen onSetupComplete={handleSetupComplete} />;
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      {renderCurrentScreen()}

      {/* Upload Modal */}
      <UploadModal
        visible={showUploadModal}
        currentPath={currentPath}
        onClose={() => setShowUploadModal(false)}
        onFilesSelected={handleFilesSelected}
      />

      {/* Progress Modal */}
      <ProgressModal
        visible={showProgressModal}
        operations={state.operations}
        onClose={() => setShowProgressModal(false)}
        onCancelOperation={(id) => updateFileOperation(id, { status: 'cancelled' })}
        onRetryOperation={(id) => {
          // Implement retry logic here
          updateFileOperation(id, { status: 'pending' });
        }}
      />

      {/* Action Sheet */}
      <ActionSheet
        visible={showActionSheet}
        item={selectedFile}
        onClose={() => setShowActionSheet(false)}
        onDownload={handleDownload}
        onShare={handleShare}
        onRename={(item) => Alert.alert('Rename', 'Rename functionality not implemented yet')}
        onMove={(item) => Alert.alert('Move', 'Move functionality not implemented yet')}
        onCopy={(item) => Alert.alert('Copy', 'Copy functionality not implemented yet')}
        onDelete={handleDelete}
        onViewDetails={(item) => Alert.alert('Details', `File: ${item.name}\nSize: ${item.size} bytes`)}
      />
    </View>
  );
};

export default function App() {
  return (
    <AppProvider>
      <AppContent />
    </AppProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
});
