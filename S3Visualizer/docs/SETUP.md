# Setup Guide

This guide will help you set up the S3 Visualizer app from scratch.

## Prerequisites

### System Requirements
- **Node.js**: Version 18 or higher
- **npm** or **yarn**: Latest version
- **React Native CLI**: Latest version
- **Git**: For version control

### Development Environment

#### For iOS Development
- **macOS**: Required for iOS development
- **Xcode**: Latest version from App Store
- **iOS Simulator**: Included with Xcode
- **CocoaPods**: For iOS dependency management
  ```bash
  sudo gem install cocoapods
  ```

#### For Android Development
- **Android Studio**: Latest version
- **Android SDK**: API level 31 or higher
- **Android Emulator**: Set up through Android Studio
- **Java Development Kit**: JDK 11 or higher

### AWS Requirements
- **AWS Account**: Active AWS account
- **S3 Bucket**: At least one S3 bucket
- **IAM User**: With appropriate S3 permissions

## Installation Steps

### 1. Clone the Repository
```bash
git clone https://github.com/yourusername/s3-visualizer.git
cd s3-visualizer/S3Visualizer
```

### 2. Install Dependencies
```bash
# Using npm
npm install

# Using yarn
yarn install
```

### 3. Install iOS Dependencies (iOS only)
```bash
cd ios
pod install
cd ..
```

### 4. Configure Development Environment

#### React Native Environment Setup
Follow the official React Native environment setup guide:
- [React Native - Environment Setup](https://reactnative.dev/docs/environment-setup)

#### Verify Installation
```bash
# Check React Native installation
npx react-native --version

# Check Node.js version
node --version

# Check npm/yarn version
npm --version
# or
yarn --version
```

## AWS Configuration

### 1. Create IAM User

1. **Go to AWS IAM Console**
   - Navigate to [AWS IAM Console](https://console.aws.amazon.com/iam/)
   - Click "Users" in the left sidebar
   - Click "Add users"

2. **Configure User**
   - User name: `s3-visualizer-user` (or your preferred name)
   - Access type: Select "Programmatic access"
   - Click "Next: Permissions"

3. **Set Permissions**
   - Choose "Attach existing policies directly"
   - Search for and select `AmazonS3FullAccess` (for testing)
   - Or create a custom policy (recommended for production)

4. **Custom Policy (Recommended)**
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Sid": "S3VisualizerAccess",
         "Effect": "Allow",
         "Action": [
           "s3:ListBucket",
           "s3:GetBucketLocation",
           "s3:ListBucketMultipartUploads"
         ],
         "Resource": "arn:aws:s3:::your-bucket-name"
       },
       {
         "Sid": "S3VisualizerObjectAccess",
         "Effect": "Allow",
         "Action": [
           "s3:GetObject",
           "s3:PutObject",
           "s3:DeleteObject",
           "s3:GetObjectAcl",
           "s3:PutObjectAcl",
           "s3:AbortMultipartUpload",
           "s3:ListMultipartUploadParts"
         ],
         "Resource": "arn:aws:s3:::your-bucket-name/*"
       }
     ]
   }
   ```

5. **Complete User Creation**
   - Review settings
   - Click "Create user"
   - **Important**: Save the Access Key ID and Secret Access Key

### 2. Create S3 Bucket

1. **Go to S3 Console**
   - Navigate to [AWS S3 Console](https://console.aws.amazon.com/s3/)
   - Click "Create bucket"

2. **Configure Bucket**
   - Bucket name: Choose a unique name (e.g., `my-s3-visualizer-bucket`)
   - Region: Choose your preferred region
   - Keep default settings for now
   - Click "Create bucket"

3. **Configure CORS (Optional)**
   If you plan to access files via web browser:
   ```json
   [
     {
       "AllowedHeaders": ["*"],
       "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
       "AllowedOrigins": ["*"],
       "ExposeHeaders": ["ETag"],
       "MaxAgeSeconds": 3000
     }
   ]
   ```

## Running the App

### 1. Start Metro Bundler
```bash
npm start
# or
yarn start
```

### 2. Run on iOS Simulator
```bash
npm run ios
# or
yarn ios

# For specific simulator
npx react-native run-ios --simulator="iPhone 14"
```

### 3. Run on Android Emulator
```bash
npm run android
# or
yarn android

# For specific device
npx react-native run-android --deviceId=emulator-5554
```

### 4. Run on Physical Device

#### iOS Device
1. Connect your iOS device via USB
2. Open `ios/S3Visualizer.xcworkspace` in Xcode
3. Select your device from the device list
4. Click the "Run" button

#### Android Device
1. Enable Developer Options and USB Debugging on your device
2. Connect via USB
3. Run: `npx react-native run-android`

## First Launch Configuration

### 1. App Setup Screen
When you first launch the app, you'll see the setup screen:

1. **Access Key ID**: Enter your AWS Access Key ID
2. **Secret Access Key**: Enter your AWS Secret Access Key
3. **Region**: Select your S3 bucket region
4. **Bucket Name**: Enter your S3 bucket name

### 2. Test Connection
- Tap "Test Connection" to verify your credentials
- If successful, tap "Save & Continue"
- Your credentials will be securely stored on the device

### 3. Start Using the App
- You'll be taken to the home screen
- Tap "Browse Files" to start exploring your S3 bucket

## Troubleshooting

### Common Issues

#### Metro Bundler Issues
```bash
# Clear Metro cache
npx react-native start --reset-cache

# Clear npm cache
npm cache clean --force
```

#### iOS Build Issues
```bash
# Clean iOS build
cd ios
xcodebuild clean
cd ..

# Reinstall pods
cd ios
pod deintegrate
pod install
cd ..
```

#### Android Build Issues
```bash
# Clean Android build
cd android
./gradlew clean
cd ..

# Reset Android project
npx react-native run-android --reset-cache
```

#### AWS Connection Issues
- Verify your AWS credentials are correct
- Check that your IAM user has the necessary permissions
- Ensure your S3 bucket exists and is in the correct region
- Check your internet connection

### Getting Help

If you encounter issues:

1. **Check the logs**: Look at Metro bundler output and device logs
2. **Search existing issues**: Check GitHub issues for similar problems
3. **Create a new issue**: Provide detailed information about your setup and the error

## Next Steps

Once you have the app running:

1. **Read the User Guide**: [USER_GUIDE.md](USER_GUIDE.md)
2. **Explore Features**: Try uploading, downloading, and organizing files
3. **Customize Settings**: Adjust app preferences in the Settings screen
4. **Test on Different Devices**: Ensure compatibility across devices

## Development Tips

### Hot Reloading
- Enable Fast Refresh for instant code updates
- Use `console.log()` for debugging
- Install React Native Debugger for advanced debugging

### Code Quality
```bash
# Run linter
npm run lint

# Run type checker
npm run type-check

# Run tests
npm test
```

### Performance
- Use React Native Performance Monitor
- Profile your app regularly
- Optimize images and large file handling

---

**Need help?** Check our [Troubleshooting Guide](TROUBLESHOOTING.md) or reach out to our support team.
