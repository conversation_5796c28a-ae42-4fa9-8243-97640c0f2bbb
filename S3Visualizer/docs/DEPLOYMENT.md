# Deployment Guide

This guide covers how to build and deploy the S3 Visualizer app to production.

## Pre-deployment Checklist

### Code Quality
- [ ] All tests pass (`npm test`)
- [ ] Code linting passes (`npm run lint`)
- [ ] TypeScript compilation succeeds (`npm run type-check`)
- [ ] No console.log statements in production code
- [ ] All TODO comments addressed
- [ ] Code review completed

### Configuration
- [ ] Production configuration files updated
- [ ] Environment variables set correctly
- [ ] API endpoints point to production
- [ ] Debug mode disabled
- [ ] Analytics and crash reporting configured
- [ ] App icons and splash screens updated

### Testing
- [ ] Manual testing completed on target devices
- [ ] Performance testing passed
- [ ] Security testing completed
- [ ] Accessibility testing verified
- [ ] Beta testing feedback incorporated

## iOS Deployment

### Prerequisites
- **Apple Developer Account**: Active paid developer account
- **Xcode**: Latest stable version
- **Certificates**: Valid distribution certificates
- **Provisioning Profiles**: App Store distribution profile
- **App Store Connect**: App configured in App Store Connect

### Build Configuration

#### 1. Update Version Numbers
```bash
# Update version in package.json
npm version patch  # or minor/major

# Update iOS version numbers
# Edit ios/S3Visualizer/Info.plist
# CFBundleShortVersionString: 1.0.0
# CFBundleVersion: 1
```

#### 2. Configure Release Settings
```bash
# Open Xcode project
open ios/S3Visualizer.xcworkspace

# In Xcode:
# 1. Select S3Visualizer project
# 2. Select S3Visualizer target
# 3. Go to Build Settings
# 4. Set "Release" configuration
# 5. Ensure "Dead Code Stripping" is enabled
# 6. Set "Strip Debug Symbols During Copy" to YES
```

#### 3. Build for Release
```bash
# Clean build folder
cd ios
xcodebuild clean -workspace S3Visualizer.xcworkspace -scheme S3Visualizer

# Build for release
xcodebuild archive \
  -workspace S3Visualizer.xcworkspace \
  -scheme S3Visualizer \
  -configuration Release \
  -archivePath S3Visualizer.xcarchive
```

### App Store Submission

#### 1. Create App Store Listing
1. **Go to App Store Connect**
2. **Create New App**:
   - Platform: iOS
   - Name: S3 Visualizer
   - Primary Language: English
   - Bundle ID: com.yourcompany.s3visualizer
   - SKU: unique identifier

#### 2. Upload Build
```bash
# Using Xcode
# 1. Open Organizer (Window > Organizer)
# 2. Select your archive
# 3. Click "Distribute App"
# 4. Choose "App Store Connect"
# 5. Follow the upload process

# Or using command line
xcrun altool --upload-app \
  --type ios \
  --file S3Visualizer.ipa \
  --username your-apple-id \
  --password your-app-specific-password
```

#### 3. Configure App Store Listing
- **App Information**: Description, keywords, category
- **Pricing**: Free or paid pricing
- **App Privacy**: Privacy policy and data usage
- **Screenshots**: Required screenshots for all device sizes
- **App Review Information**: Contact info and review notes

#### 4. Submit for Review
1. **Select build** in App Store Connect
2. **Complete all required fields**
3. **Submit for review**
4. **Monitor review status**

### iOS Build Optimization

#### App Size Optimization
```bash
# Enable bitcode (in Build Settings)
ENABLE_BITCODE = YES

# Optimize images
# Use appropriate image formats and sizes
# Remove unused images

# Strip unused code
DEAD_CODE_STRIPPING = YES
STRIP_INSTALLED_PRODUCT = YES
```

#### Performance Optimization
```bash
# Enable optimizations
GCC_OPTIMIZATION_LEVEL = s  # Optimize for size
SWIFT_OPTIMIZATION_LEVEL = -O  # Optimize for speed

# Disable debug features
DEBUG_INFORMATION_FORMAT = dwarf
GCC_GENERATE_DEBUGGING_SYMBOLS = NO
```

## Android Deployment

### Prerequisites
- **Google Play Console**: Developer account
- **Android Studio**: Latest stable version
- **Keystore**: Release signing keystore
- **Gradle**: Configured for release builds

### Build Configuration

#### 1. Generate Release Keystore
```bash
# Generate keystore (one-time setup)
keytool -genkeypair -v \
  -storetype PKCS12 \
  -keystore s3visualizer-release.keystore \
  -alias s3visualizer \
  -keyalg RSA \
  -keysize 2048 \
  -validity 10000

# Store keystore securely and backup!
```

#### 2. Configure Gradle for Release
```gradle
// android/app/build.gradle
android {
    signingConfigs {
        release {
            storeFile file('s3visualizer-release.keystore')
            storePassword 'your-store-password'
            keyAlias 's3visualizer'
            keyPassword 'your-key-password'
        }
    }
    
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
```

#### 3. Update Version Numbers
```gradle
// android/app/build.gradle
android {
    defaultConfig {
        versionCode 1
        versionName "1.0.0"
    }
}
```

### Build Release APK/AAB

#### Build APK
```bash
cd android
./gradlew assembleRelease

# APK location: android/app/build/outputs/apk/release/app-release.apk
```

#### Build AAB (Recommended for Play Store)
```bash
cd android
./gradlew bundleRelease

# AAB location: android/app/build/outputs/bundle/release/app-release.aab
```

### Google Play Store Submission

#### 1. Create Play Console Listing
1. **Go to Google Play Console**
2. **Create Application**:
   - App name: S3 Visualizer
   - Default language: English
   - App or game: App
   - Free or paid: Free

#### 2. Upload Release
1. **Go to Release Management > App Releases**
2. **Create Release** in Production track
3. **Upload AAB file**
4. **Add release notes**
5. **Review and rollout**

#### 3. Configure Store Listing
- **Product Details**: Title, short description, full description
- **Graphics**: Icon, feature graphic, screenshots
- **Categorization**: App category and content rating
- **Contact Details**: Website, email, privacy policy
- **Pricing & Distribution**: Countries and pricing

#### 4. Content Rating
1. **Complete content rating questionnaire**
2. **Get rating certificates**
3. **Apply ratings to your app**

### Android Build Optimization

#### ProGuard Configuration
```proguard
# android/app/proguard-rules.pro

# Keep AWS SDK classes
-keep class com.amazonaws.** { *; }
-keep class com.amazon.** { *; }

# Keep React Native classes
-keep class com.facebook.react.** { *; }

# Keep app-specific classes
-keep class com.s3visualizer.** { *; }
```

#### App Bundle Optimization
```gradle
// android/app/build.gradle
android {
    bundle {
        language {
            enableSplit = true
        }
        density {
            enableSplit = true
        }
        abi {
            enableSplit = true
        }
    }
}
```

## Testing Production Builds

### iOS Testing
```bash
# Test on device
npx react-native run-ios --configuration Release --device

# Test with TestFlight
# 1. Upload build to App Store Connect
# 2. Add internal testers
# 3. Distribute for testing
```

### Android Testing
```bash
# Install release APK
adb install android/app/build/outputs/apk/release/app-release.apk

# Test with Internal Testing
# 1. Upload AAB to Play Console
# 2. Create internal testing release
# 3. Add test users
```

## Monitoring and Analytics

### Crash Reporting
```javascript
// Configure crash reporting (e.g., Crashlytics)
import crashlytics from '@react-native-firebase/crashlytics';

// Enable crash reporting in production
if (!__DEV__) {
  crashlytics().setCrashlyticsCollectionEnabled(true);
}
```

### Analytics
```javascript
// Configure analytics (e.g., Firebase Analytics)
import analytics from '@react-native-firebase/analytics';

// Track app usage
analytics().logEvent('app_launch');
```

### Performance Monitoring
```javascript
// Configure performance monitoring
import perf from '@react-native-firebase/perf';

// Monitor app performance
const trace = await perf().startTrace('app_startup');
// ... app startup code
await trace.stop();
```

## Post-Deployment

### Monitoring
- [ ] Monitor crash reports
- [ ] Check app store reviews
- [ ] Monitor performance metrics
- [ ] Track user analytics
- [ ] Monitor AWS usage and costs

### Updates
- [ ] Plan regular updates
- [ ] Monitor for security vulnerabilities
- [ ] Update dependencies regularly
- [ ] Respond to user feedback
- [ ] Plan feature roadmap

### Support
- [ ] Set up user support channels
- [ ] Create FAQ and help documentation
- [ ] Monitor support requests
- [ ] Plan bug fix releases
- [ ] Maintain app store listings

## Rollback Plan

### iOS Rollback
1. **Remove from sale** in App Store Connect
2. **Reject current version** if in review
3. **Submit previous version** if needed
4. **Communicate with users** about issues

### Android Rollback
1. **Halt rollout** in Play Console
2. **Roll back to previous version**
3. **Fix issues** in new release
4. **Resume rollout** when ready

## Security Considerations

### Code Protection
- [ ] Obfuscate sensitive code
- [ ] Remove debug information
- [ ] Validate all inputs
- [ ] Use secure communication
- [ ] Implement certificate pinning

### Data Protection
- [ ] Encrypt sensitive data
- [ ] Use secure storage
- [ ] Implement proper authentication
- [ ] Follow privacy regulations
- [ ] Regular security audits

---

**Need deployment help?** Contact the development team or check our [troubleshooting guide](TROUBLESHOOTING.md).
