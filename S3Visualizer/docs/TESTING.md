# Testing Guide

This guide covers how to test the S3 Visualizer app thoroughly before deployment.

## Testing Overview

### Testing Strategy
- **Unit Tests**: Test individual functions and components
- **Integration Tests**: Test service interactions and workflows
- **End-to-End Tests**: Test complete user journeys
- **Manual Testing**: Verify UI/UX and edge cases
- **Performance Testing**: Ensure app performs well under load

### Test Environment Setup
- **Test AWS Account**: Separate from production
- **Test S3 Buckets**: Dedicated buckets for testing
- **Test Data**: Sample files of various types and sizes
- **Device Testing**: Multiple devices and OS versions

## Running Tests

### Automated Tests

#### Unit Tests
```bash
# Run all unit tests
npm test
# or
yarn test

# Run tests in watch mode
npm test -- --watch

# Run tests with coverage
npm test -- --coverage

# Run specific test file
npm test -- utils.test.ts
```

#### Integration Tests
```bash
# Run integration tests
npm run test:integration

# Run with specific configuration
npm run test:integration -- --config=jest.integration.config.js
```

#### End-to-End Tests
```bash
# Run E2E tests (requires Detox setup)
npm run test:e2e

# Run on specific platform
npm run test:e2e:ios
npm run test:e2e:android
```

### Test Coverage
```bash
# Generate coverage report
npm run test:coverage

# View coverage report
open coverage/lcov-report/index.html
```

## Manual Testing Checklist

### 1. Initial Setup and Authentication

#### First Launch
- [ ] App launches without crashes
- [ ] Setup screen displays correctly
- [ ] All input fields are accessible
- [ ] Keyboard navigation works properly

#### Credential Entry
- [ ] Can enter AWS Access Key ID
- [ ] Can enter Secret Access Key
- [ ] Region picker works correctly
- [ ] Bucket name input accepts valid names
- [ ] Input validation shows appropriate errors

#### Connection Testing
- [ ] "Test Connection" button works
- [ ] Valid credentials show success message
- [ ] Invalid credentials show error message
- [ ] Network errors are handled gracefully
- [ ] Loading states display correctly

#### Credential Storage
- [ ] "Save & Continue" stores credentials securely
- [ ] Biometric authentication prompts (if enabled)
- [ ] App remembers credentials on restart
- [ ] Can clear credentials from settings

### 2. Home Screen

#### Dashboard Display
- [ ] Storage statistics load correctly
- [ ] File/folder counts are accurate
- [ ] Total size calculation is correct
- [ ] Last updated timestamp shows

#### Navigation
- [ ] "Browse Files" button works
- [ ] "Settings" button works
- [ ] Logout functionality works
- [ ] Pull-to-refresh updates statistics

### 3. File Browser

#### File Listing
- [ ] Files and folders display correctly
- [ ] File icons match file types
- [ ] File sizes are formatted properly
- [ ] Dates are displayed correctly
- [ ] Empty folders show appropriate message

#### Navigation
- [ ] Can enter folders by tapping
- [ ] Breadcrumb navigation works
- [ ] Back button returns to parent folder
- [ ] Deep folder navigation works

#### View Modes
- [ ] Can switch between list and grid views
- [ ] Grid view shows thumbnails (when available)
- [ ] View preference is remembered
- [ ] Both views handle large file lists

### 4. File Operations

#### File Upload
- [ ] Upload button opens file picker
- [ ] Can select single files
- [ ] Can select multiple files
- [ ] File picker shows all file types
- [ ] Image picker works for photos
- [ ] Upload progress displays correctly
- [ ] Can cancel uploads
- [ ] Large files use multipart upload
- [ ] Upload errors are handled gracefully

#### File Download
- [ ] Tapping files opens action menu
- [ ] Download generates valid URLs
- [ ] Download links work in browser
- [ ] Download progress shows (if applicable)
- [ ] Download errors are handled

#### File Actions
- [ ] Action sheet displays all options
- [ ] Share generates valid presigned URLs
- [ ] Rename functionality works
- [ ] Move files between folders
- [ ] Copy files works correctly
- [ ] Delete confirms before removing
- [ ] File details show correct information

### 5. Folder Management

#### Folder Creation
- [ ] Can create new folders
- [ ] Folder names are validated
- [ ] Invalid characters are rejected
- [ ] Duplicate names are handled
- [ ] Nested folder creation works

#### Folder Operations
- [ ] Can enter folders
- [ ] Can navigate back from folders
- [ ] Can delete empty folders
- [ ] Can delete folders with contents (with warning)
- [ ] Folder actions menu works

### 6. Search and Filter

#### Search Functionality
- [ ] Search bar accepts input
- [ ] Search results update in real-time
- [ ] Can clear search
- [ ] Search works across file names
- [ ] Search handles special characters
- [ ] Empty search results show message

#### Filtering
- [ ] File type filters work
- [ ] Date range filters work
- [ ] Size filters work
- [ ] Multiple filters can be combined
- [ ] Filter state is maintained during navigation

### 7. Batch Operations

#### Selection Mode
- [ ] Long press enters selection mode
- [ ] Can select multiple files
- [ ] Selection indicators are clear
- [ ] Can deselect items
- [ ] Can select all items
- [ ] Can cancel selection mode

#### Batch Actions
- [ ] Delete multiple files works
- [ ] Move multiple files works
- [ ] Batch operations show progress
- [ ] Partial failures are handled
- [ ] Can cancel batch operations

### 8. Settings

#### Display Settings
- [ ] View mode toggle works
- [ ] Sort options work correctly
- [ ] Sort order toggle works
- [ ] Hidden files toggle works
- [ ] Settings are persisted

#### Sync Settings
- [ ] Auto-refresh toggle works
- [ ] Refresh interval selection works
- [ ] Background sync works (if implemented)

#### Security Settings
- [ ] Can clear credentials
- [ ] Can clear all data
- [ ] Biometric settings work (if available)
- [ ] Confirmation dialogs appear

### 9. Error Handling

#### Network Errors
- [ ] Offline state is detected
- [ ] Network errors show user-friendly messages
- [ ] App gracefully handles connection loss
- [ ] Retry mechanisms work

#### AWS Errors
- [ ] Invalid credentials show appropriate errors
- [ ] Permission errors are handled
- [ ] Bucket not found errors are clear
- [ ] Rate limiting is handled

#### App Errors
- [ ] Crashes are prevented
- [ ] Error boundaries catch exceptions
- [ ] Error messages are helpful
- [ ] App can recover from errors

### 10. Performance

#### Loading Performance
- [ ] App launches quickly
- [ ] File lists load in reasonable time
- [ ] Large folders don't freeze the app
- [ ] Smooth scrolling in file lists

#### Memory Usage
- [ ] App doesn't consume excessive memory
- [ ] Memory usage is stable over time
- [ ] Large file operations don't cause crashes
- [ ] Background memory usage is reasonable

#### Battery Usage
- [ ] App doesn't drain battery excessively
- [ ] Background operations are optimized
- [ ] Upload/download operations are efficient

## Device Testing

### iOS Testing
- [ ] iPhone (various models and iOS versions)
- [ ] iPad (portrait and landscape)
- [ ] Different screen sizes and resolutions
- [ ] iOS-specific features (Face ID, Touch ID)

### Android Testing
- [ ] Various Android devices and versions
- [ ] Different screen sizes and densities
- [ ] Android-specific features (fingerprint, etc.)
- [ ] Different manufacturers (Samsung, Google, etc.)

## Performance Testing

### Load Testing
- [ ] Test with large numbers of files (1000+)
- [ ] Test with large file sizes (GB files)
- [ ] Test concurrent operations
- [ ] Test memory usage under load

### Stress Testing
- [ ] Rapid navigation between screens
- [ ] Multiple simultaneous uploads
- [ ] Network interruption during operations
- [ ] Low memory conditions

## Security Testing

### Credential Security
- [ ] Credentials are encrypted at rest
- [ ] Credentials are not logged
- [ ] Biometric authentication works
- [ ] Session management is secure

### Network Security
- [ ] All communications use HTTPS
- [ ] Certificate validation works
- [ ] No sensitive data in URLs
- [ ] Proper error handling for security failures

## Accessibility Testing

### Screen Reader Testing
- [ ] VoiceOver (iOS) navigation works
- [ ] TalkBack (Android) navigation works
- [ ] All elements have proper labels
- [ ] Navigation order is logical

### Visual Accessibility
- [ ] High contrast mode works
- [ ] Large text scaling works
- [ ] Color blind accessibility
- [ ] Reduced motion support

### Motor Accessibility
- [ ] Large touch targets
- [ ] Voice control support
- [ ] Switch control compatibility

## Regression Testing

### Before Each Release
- [ ] Run full automated test suite
- [ ] Execute critical path manual tests
- [ ] Test on primary target devices
- [ ] Verify no existing functionality is broken

### Test Data Management
- [ ] Use consistent test data sets
- [ ] Clean up test data after tests
- [ ] Maintain test AWS accounts
- [ ] Document test scenarios

## Reporting Issues

### Bug Report Template
```
**Bug Description**: Clear description of the issue
**Steps to Reproduce**: Numbered steps to reproduce
**Expected Behavior**: What should happen
**Actual Behavior**: What actually happens
**Device Info**: Device model, OS version, app version
**Screenshots**: If applicable
**Logs**: Relevant error logs or console output
```

### Performance Issue Template
```
**Performance Issue**: Description of the performance problem
**Affected Feature**: Which part of the app is slow
**Device Info**: Device specifications
**Test Conditions**: Network, file sizes, etc.
**Measurements**: Specific timing or memory usage data
**Impact**: How it affects user experience
```

---

**Need help with testing?** Contact the development team or check our [Troubleshooting Guide](TROUBLESHOOTING.md).
